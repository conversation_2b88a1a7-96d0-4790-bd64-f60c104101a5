import path from 'path'
import { app } from 'electron'

/** 当前应用程序所在目录 
 * win:C:\Users\<USER>\AppData\Local\{应用名称}\app-{版本号}\resources\app.asar
 * mac:/Applications/{应用名称}.app/Contents/Resources/app.asar
 * linux:/opt/{应用名称}/resources/app.asar
*/
export const APP_PATH: string = app.getAppPath()


/** 储存你应用程序设置文件的文件夹，适用于存储应用配置、SQLite数据库、需要在重启后保留的缓存等
 * win:C:\Users\<USER>\AppData\Roaming\{应用名称}
 * mac:/Users/<USER>/Library/Application Support/{应用名称}
 * linux:/home/<USER>/.config/{应用名称}
*/
export const USER_DATA_PATH: string = app.getPath('userData')

/** 应用程序的执行文件夹，设置自动启动、自动更新、子进程启动等情形下使用
 * win:C:\Users\<USER>\AppData\Local\Programs\{应用名称}\{应用名称}.exe
 * mac:/Applications/{应用名称}.app/Contents/MacOS/{应用名称}
 * linux:/usr/bin/{应用名称}
*/
export const EXE_PATH: string = app.getPath('exe')

/** 
 * 应用程序的临时文件夹 ，适用于存储临时下载、解压文件、不需要保留的缓存等
 * win:C:\Users\<USER>\AppData\Local\Temp
 * mac:/var/folders/{随机字符}/{随机字符}/T/
 * linux:/tmp
*/
export const TEMP_PATH: string = app.getPath('temp')

/** 应用程序的日志文件夹 */
export const LOGS_PATH: string =
  process.platform === 'darwin'
    ? path.resolve(app.getPath('logs'), `../${app.name}`)
    : path.resolve(USER_DATA_PATH, 'logs')

/** 资源文件夹 */
export const ASSETS_PATH: string =
  process.env.NODE_ENV === 'development'
    ? 'assets'
    : path.join(APP_PATH, 'assets')

export const DESKTOP_PATH = app.getPath('desktop')
export const DOCUMENTS_PATH = app.getPath('documents')
export const DOWNLOADS_PATH = app.getPath('downloads')
export const MUSIC_PATH = app.getPath('music')
export const PICTURES_PATH = app.getPath('pictures')
export const VIDEOS_PATH = app.getPath('videos')


