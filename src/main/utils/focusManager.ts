import { spawn } from 'child_process';
import * as log from '../logging';

/**
 * 统一的焦点管理工具
 * 处理剪贴板操作时的焦点记录和恢复
 */
export class FocusManager {
  private static previousFocusedApp: string | null = null;
  private static previousFocusedBundleId: string | null = null;
  private static currentFinderPath: string | null = null;

  /**
   * 记录当前焦点应用
   */
  public static async rememberCurrentFocus(): Promise<void> {
    const startTime = Date.now();
    try {
      const platform = process.platform;
      log.debug('焦点管理器开始记录当前焦点，平台:', platform);

      const getCurrentFocus = (): Promise<{appName: string | null, bundleId: string | null}> => {
        return new Promise((resolve) => {
          let child: any;

          if (platform === 'darwin') {
            // 使用更精确的方法获取前台应用，包括Bundle ID和应用名称
            child = spawn('osascript', ['-e', `
              tell application "System Events"
                set frontApp to first application process whose frontmost is true
                set appName to name of frontApp
                set bundleId to ""
                try
                  set bundleId to bundle identifier of frontApp
                end try
                return appName & "|" & bundleId
              end tell
            `], {
              stdio: 'pipe'
            });
          } else if (platform === 'win32') {
            child = spawn('powershell', ['-Command', 'Get-Process | Where-Object {$_.MainWindowTitle -ne ""} | Select-Object -First 1 ProcessName'], {
              stdio: 'pipe',
              windowsHide: true
            });
          } else {
            // Linux
            child = spawn('xdotool', ['getactivewindow', 'getwindowname'], {
              stdio: 'pipe'
            });
          }

          let output = '';
          let errorOutput = '';

          child.stdout.on('data', (data: Buffer) => {
            output += data.toString();
          });

          child.stderr?.on('data', (data: Buffer) => {
            errorOutput += data.toString();
          });

          child.on('close', (code: number | null) => {
            if (code !== 0 && errorOutput) {
              log.warn('焦点获取命令执行警告:', { code, error: errorOutput });
            }

            let appName = output.trim();
            let bundleId = '';

            if (platform === 'darwin') {
              // 解析 "appName|bundleId" 格式
              const parts = appName.split('|');
              if (parts.length >= 2) {
                appName = parts[0].trim();
                bundleId = parts[1].trim();

                // 对于Electron应用，使用Bundle ID来区分不同的应用
                if (appName === 'Electron' && bundleId) {
                  if (bundleId === 'com.microsoft.VSCode') {
                    appName = 'Visual Studio Code';
                  } else if (bundleId === 'com.microsoft.VSCodeInsiders') {
                    appName = 'Visual Studio Code Insiders';
                  } else if (bundleId === 'com.todesktop.230313mzl4w4u92') {
                    appName = 'Cursor';
                  } else if (bundleId.includes('cursor')) {
                    appName = 'Cursor';
                  } else if (bundleId === 'com.trae.app') {
                    appName = 'Trae (VS Code based)';
                  } else if (bundleId.includes('vscode') || bundleId.includes('code')) {
                    appName = `VS Code variant (${bundleId})`;
                  } else {
                    // 其他Electron应用，保持原名称但添加Bundle ID信息
                    appName = `${appName}:${bundleId}`;
                  }
                }

                log.debug('焦点管理器解析应用信息:', { appName, bundleId });
              }
            } else if (platform === 'win32') {
              const lines = appName.split('\n');
              if (lines.length > 1) {
                appName = lines[1].trim();
              } else {
                appName = '';
              }
            }

            // 过滤掉自己的应用
            if (appName && appName !== 'Aido' && !appName.includes('Aido')) {
              log.debug('焦点管理器检测到有效应用:', appName);
              resolve({appName, bundleId});
            } else {
              log.debug('焦点管理器过滤掉无效应用:', appName || '(空)');
              resolve({appName: null, bundleId: null});
            }
          });

          child.on('error', (error: Error) => {
            log.warn('焦点获取命令执行错误:', error.message);
            resolve({appName: null, bundleId: null});
          });

          // 增加超时时间，确保有足够时间获取焦点
          setTimeout(() => {
            child.kill();
            log.warn('焦点获取超时');
            resolve({appName: null, bundleId: null});
          }, 1000);
        });
      };

      const focusResult = await getCurrentFocus();
      const previousApp = this.previousFocusedApp;
      this.previousFocusedApp = focusResult.appName;
      this.previousFocusedBundleId = focusResult.bundleId;

      // 如果当前应用是Finder，同时获取当前文件夹路径
      if (focusResult.appName === 'Finder') {
        this.currentFinderPath = await this.getCurrentFinderPath();
        if (this.currentFinderPath) {
          log.info('焦点管理器记录Finder路径:', this.currentFinderPath);
        }
      } else {
        this.currentFinderPath = null;
      }

      const duration = Date.now() - startTime;
      if (focusResult.appName) {
        const bundleInfo = focusResult.bundleId ? ` (Bundle: ${focusResult.bundleId})` : '';
        log.info(`焦点管理器记录应用: ${focusResult.appName}${bundleInfo} (耗时: ${duration}ms, 前一个: ${previousApp || '无'})`);
      } else {
        log.warn(`焦点管理器未能获取当前焦点应用 (耗时: ${duration}ms, 前一个: ${previousApp || '无'})`);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      log.error(`焦点管理器记录焦点失败 (耗时: ${duration}ms):`, error);
      this.previousFocusedApp = null;
    }
  }

  /**
   * 恢复到记录的焦点应用
   */
  public static async restorePreviousFocus(): Promise<boolean> {
    if (!this.previousFocusedApp) {
      log.warn('焦点管理器恢复失败: 没有记录的焦点应用');
      return false;
    }

    try {
      const platform = process.platform;
      
      if (platform === 'darwin') {
        // macOS: 使用Bundle ID精确激活应用（如果有的话）
        let activateScript = '';

        if (this.previousFocusedBundleId) {
          // 优先使用Bundle ID激活，更精确
          activateScript = `tell application id "${this.previousFocusedBundleId}" to activate`;
          log.debug('焦点管理器使用Bundle ID激活应用:', { app: this.previousFocusedApp, bundleId: this.previousFocusedBundleId });
        } else {
          // 回退到应用名称激活
          activateScript = `tell application "${this.previousFocusedApp}" to activate`;
          log.debug('焦点管理器使用应用名称激活应用:', this.previousFocusedApp);
        }

        spawn('osascript', ['-e', activateScript], {
          stdio: 'ignore',
          detached: true
        });
        await new Promise(resolve => setTimeout(resolve, 300));
        log.debug('焦点管理器已激活 macOS 应用:', this.previousFocusedApp);
        return true;
      } else if (platform === 'win32') {
        // Windows: 激活应用窗口
        spawn('powershell', ['-Command', `
          $proc = Get-Process -Name "${this.previousFocusedApp}" -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -ne ""}
          if ($proc -and $proc.MainWindowHandle -ne [System.IntPtr]::Zero) {
            Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport("user32.dll")] public static extern bool SetForegroundWindow(IntPtr hWnd); }'
            [Win32]::SetForegroundWindow($proc.MainWindowHandle)
          }
        `], {
          stdio: 'ignore',
          detached: true,
          windowsHide: true
        });
        await new Promise(resolve => setTimeout(resolve, 300));
        log.debug('焦点管理器已激活 Windows 应用:', this.previousFocusedApp);
        return true;
      }
      
      return false;
    } catch (error) {
      log.error('焦点管理器恢复焦点失败:', error);
      return false;
    }
  }

  /**
   * 获取记录的焦点应用
   */
  public static getPreviousFocusedApp(): string | null {
    return this.previousFocusedApp;
  }

  /**
   * 获取记录的焦点应用的Bundle ID
   */
  public static getPreviousFocusedBundleId(): string | null {
    return this.previousFocusedBundleId;
  }

  /**
   * 手动设置焦点应用（用于兼容现有代码）
   */
  public static setPreviousFocusedApp(appName: string | null): void {
    this.previousFocusedApp = appName;
    if (appName) {
      log.debug('焦点管理器手动设置应用:', appName);
    }
  }

  /**
   * 清除记录的焦点应用
   */
  public static clearPreviousFocus(): void {
    this.previousFocusedApp = null;
    this.previousFocusedBundleId = null;
    this.currentFinderPath = null;
  }

  /**
   * 获取Finder中当前打开的文件夹路径
   */
  public static async getCurrentFinderPath(): Promise<string | null> {
    if (process.platform !== 'darwin') {
      return null; // 只支持macOS
    }

    return new Promise((resolve) => {
      try {
        const child = spawn('osascript', ['-e', `
          tell application "Finder"
            if (count of Finder windows) > 0 then
              set currentWindow to window 1
              set currentFolder to (target of currentWindow) as alias
              return POSIX path of currentFolder
            else
              return ""
            end if
          end tell
        `], {
          stdio: 'pipe'
        });

        let output = '';
        child.stdout.on('data', (data: Buffer) => {
          output += data.toString();
        });

        child.on('close', (code) => {
          const path = output.trim();
          if (code === 0 && path && path !== '') {
            resolve(path);
          } else {
            resolve(null);
          }
        });

        child.on('error', () => resolve(null));

        setTimeout(() => {
          child.kill();
          resolve(null);
        }, 1000);
      } catch (error) {
        log.error('获取Finder路径失败:', error);
        resolve(null);
      }
    });
  }

  /**
   * 获取记录的Finder路径
   */
  public static getCurrentFinderPathCached(): string | null {
    return this.currentFinderPath;
  }

  /**
   * 检查当前是否在Finder中并返回路径
   */
  public static async getActiveFinderPath(): Promise<string | null> {
    if (this.previousFocusedApp === 'Finder') {
      return this.currentFinderPath || await this.getCurrentFinderPath();
    }
    return null;
  }

  /**
   * 获取Finder中当前选中的文件夹路径（如果选中的是文件夹）
   */
  public static async getSelectedFinderFolder(): Promise<string | null> {
    if (process.platform !== 'darwin') {
      return null; // 只支持macOS
    }

    return new Promise((resolve) => {
      try {
        const child = spawn('osascript', ['-e', `
          tell application "Finder"
            if (count of Finder windows) > 0 then
              set currentWindow to window 1
              set selectedItems to selection of currentWindow
              if (count of selectedItems) > 0 then
                set selectedItem to item 1 of selectedItems
                if kind of selectedItem is "文件夹" or kind of selectedItem is "Folder" then
                  return POSIX path of (selectedItem as alias)
                else
                  -- 如果选中的不是文件夹，返回当前文件夹路径
                  set currentFolder to (target of currentWindow) as alias
                  return POSIX path of currentFolder
                end if
              else
                -- 没有选中项，返回当前文件夹路径
                set currentFolder to (target of currentWindow) as alias
                return POSIX path of currentFolder
              end if
            else
              return ""
            end if
          end tell
        `], {
          stdio: 'pipe'
        });

        let output = '';
        child.stdout.on('data', (data: Buffer) => {
          output += data.toString();
        });

        child.on('close', (code) => {
          const path = output.trim();
          if (code === 0 && path && path !== '') {
            resolve(path);
          } else {
            resolve(null);
          }
        });

        child.on('error', () => resolve(null));

        setTimeout(() => {
          child.kill();
          resolve(null);
        }, 1000);
      } catch (error) {
        log.error('获取Finder选中文件夹失败:', error);
        resolve(null);
      }
    });
  }

  /**
   * 获取最佳的粘贴目标路径
   * 优先级：选中的文件夹 > 当前打开的文件夹
   */
  public static async getBestPasteTargetPath(): Promise<string | null> {
    if (this.previousFocusedApp !== 'Finder') {
      return null;
    }

    try {
      // 首先尝试获取选中的文件夹
      const selectedFolder = await this.getSelectedFinderFolder();
      if (selectedFolder) {
        log.info('焦点管理器找到粘贴目标路径:', selectedFolder);
        return selectedFolder;
      }

      // 如果没有选中文件夹，使用当前文件夹
      const currentFolder = this.currentFinderPath || await this.getCurrentFinderPath();
      if (currentFolder) {
        log.info('焦点管理器使用当前文件夹作为粘贴目标:', currentFolder);
        return currentFolder;
      }

      return null;
    } catch (error) {
      log.error('获取粘贴目标路径失败:', error);
      return null;
    }
  }
} 