import path from 'path';
import fs from 'node:fs';
import { MCPConfig, MCPServer } from '../../shared/types/mcp';
import type { MCPTool } from '../../shared/types/mcpTool';
import { isUndefined, omitBy } from 'lodash';
import * as logging from '../logging';
import { dbService } from './dbService';
import { execSync } from 'child_process';

// 静态导入 MCP SDK 模块
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import { proxyMCPClient } from './mcpProxyClient';
import { getBinDir, getBunName, getUvxName } from '../utils/extract-tools';
import { USER_DATA_PATH, APP_PATH } from '../common/paths';
import { assignAgent } from './apiService';
export const DEFAULT_INHERITED_ENV_VARS =
  process.platform === 'win32'
    ? [
        'APPDATA',
        'HOMEDRIVE',
        'HOMEPATH',
        'LOCALAPPDATA',
        'PATH',
        'PROCESSOR_ARCHITECTURE',
        'SYSTEMDRIVE',
        'SYSTEMROOT',
        'TEMP',
        'USERNAME',
        'USERPROFILE',
      ]
    : /* list inspired by the default env inheritance of sudo */
      ['HOME', 'LOGNAME', 'PATH', 'SHELL', 'TERM', 'USER'];
/**
 * Returns a default environment object including only environment variables deemed safe to inherit.
 */
export function getDefaultEnvironment() {
  const env: Record<string, string> = {};
  DEFAULT_INHERITED_ENV_VARS.forEach((key) => {
    const value = process.env[key];
    if (value === undefined) {
      return;
    }
    if (value.startsWith('()')) {
      // Skip functions, which are a security risk.
      return;
    }
    env[key] = value;
  });
  return env;
}

export default class ModuleContext {
  private clients: { [key: string]: any } = {};

  private cfgPath: string;

  // 添加防重复加载的标志位
  private isLoaded = false;
  private isLoading = false;

  constructor() {
    // 根据环境选择配置文件路径
    const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === undefined;
    
    if (isDevelopment) {
      // 开发阶段：配置文件放在项目源代码中，方便团队同步
      this.cfgPath = path.join(__dirname, '../../config/mcp.json');
    } else {
      // 生产环境：配置文件放在用户数据目录
      this.cfgPath = path.join(USER_DATA_PATH, 'mcp.json');
      
      // 如果用户数据目录的配置文件不存在，从应用包中复制默认配置
      if (!fs.existsSync(this.cfgPath)) {
        this.copyDefaultConfig();
      }
    }
    
    logging.info(`MCP配置文件路径: ${this.cfgPath} (${isDevelopment ? '开发' : '生产'}环境)`);
  }

  private copyDefaultConfig(): void {
    try {
      // 默认配置文件在应用包中的路径
      const defaultConfigPath = path.join(APP_PATH, 'config/mcp.json');
      
      if (fs.existsSync(defaultConfigPath)) {
        // 确保用户数据目录存在
        const userDataDir = path.dirname(this.cfgPath);
        if (!fs.existsSync(userDataDir)) {
          fs.mkdirSync(userDataDir, { recursive: true });
        }
        
        // 复制默认配置文件
        fs.copyFileSync(defaultConfigPath, this.cfgPath);
        logging.info(`已复制默认MCP配置文件: ${defaultConfigPath} -> ${this.cfgPath}`);
      } else {
        logging.warn(`默认MCP配置文件不存在: ${defaultConfigPath}`);
      }
    } catch (error) {
      logging.error('复制默认MCP配置文件失败:', error);
    }
  }

  private static getMCPServer(key: string, server: MCPServer, config: MCPConfig) {
    let mcpSvr = config.servers[key]; // 直接通过 key 访问
    mcpSvr = {
      ...mcpSvr,
      ...omitBy({ ...server, isActive: true }, isUndefined),
    } as MCPServer;
    return mcpSvr;
  }

  // private async updateConfigAfterActivation(
  //   key: string,
  //   server: MCPServer,
  //   config: MCPConfig,
  // ) {
  //   config.servers[key] = server;
  //   await this.putConfig(config);
  // }

  private async updateConfigAfterDeactivation(key: string, config: MCPConfig) {
    if (config.servers[key]) {
      config.servers[key].isActive = false;
    }
    await this.putConfig(config);
  }

  public async getConfig() {
    const defaultConfig = { servers: {} as Record<string, MCPServer> };
    try {
      if (!fs.existsSync(this.cfgPath)) {
        fs.writeFileSync(this.cfgPath, JSON.stringify(defaultConfig, null, 2));
      }
      const config = JSON.parse(fs.readFileSync(this.cfgPath, 'utf-8'));
      
      // 兼容旧格式：如果 servers 是数组，转换为对象
      if (Array.isArray(config.servers)) {
        const serversMap: Record<string, MCPServer> = {};
        config.servers.forEach((server: any) => {
          if (server.key) {
            const { key, ...serverWithoutKey } = server;
            serversMap[key] = serverWithoutKey;
          }
        });
        config.servers = serversMap;
        // 自动保存转换后的格式
        fs.writeFileSync(this.cfgPath, JSON.stringify(config, null, 2));
      }
      
      if (!config.servers) {
        config.servers = {};
      }
      return config;
    } catch (err: any) {
      logging.captureException(err);
      return defaultConfig;
    }
  }

  public async putConfig(config: any) {
    try {
      fs.writeFileSync(this.cfgPath, JSON.stringify(config, null, 2));
      return true;
    } catch (err: any) {
      logging.captureException(err);
      return false;
    }
  }

  /**
   * 获取 MCP WebSocket URL
   * 优先从 assignAgent 接口获取，如果失败则使用默认值
   */
  private async getMcpWebSocketUrl(): Promise<string> {
    try {
      logging.info('尝试从 assignAgent 接口获取 MCP WebSocket URL...');
      const response: any = await assignAgent();
      logging.info(`assignAgent 接口获取: ${JSON.stringify(response)}`);
      // assignAgent 接口返回的数据结构
      if (response && response.data && response.data.mcpAccessAddress) {
        const url = response.data.mcpAccessAddress;
        logging.info(`成功从接口获取 WebSocket URL: ${url}`);
        return url;
      } else {
        logging.warn('assignAgent 接口返回的数据中没有 mcpAccessAddress 字段，返回数据:', JSON.stringify(response, null, 2));
        throw new Error('mcpAccessAddress not found in response');
      }
    } catch (error) {
      logging.error('从 assignAgent 接口获取 WebSocket URL 失败:', error);
      // 记录详细的错误信息
      if (error instanceof Error) {
        logging.error('API调用失败详情:', {
          message: error.message,
          name: error.name
        });
      }
      throw error; // 抛出错误而不是返回undefined
    }
  }

  public async load() {
    // 防止重复加载
    if (this.isLoaded) {
      console.log('✅ MCP Service already loaded, skipping...');
      return;
    }

    if (this.isLoading) {
      console.log('⏳ MCP Service is loading, skipping...');
      return;
    }

    this.isLoading = true;
    console.log('🔄 Starting MCP Service load...');

    try {
      // 读取文件配置
      const { servers: fileServers } = await this.getConfig();

      // 读取数据库配置
      const dbServers = dbService.getAllMcpServers();

      // 合并配置：数据库配置具有更高优先级
      const mergedServers = { ...fileServers, ...dbServers };

      logging.info(`加载MCP配置: 文件${Object.keys(fileServers).length}个, 数据库${Object.keys(dbServers).length}个, 合并后${Object.keys(mergedServers).length}个`);

      // 检查是否有配置的服务器
      if (!mergedServers || Object.keys(mergedServers).length === 0) {
        console.log('📄 No MCP servers configured, MCP service initialized with empty configuration');
        this.isLoaded = true;
        this.isLoading = false;
        return;
      }
      
      // 尝试获取 WebSocket URL，如果失败不影响本地服务器
      let websocketUrl: string | null = null;
      try {
        websocketUrl = await this.getMcpWebSocketUrl();
        logging.info(`✅ 成功获取WebSocket URL: ${websocketUrl}`);
      } catch (error) {
        logging.warn('获取WebSocket URL失败，将只使用本地MCP服务器:', error);
        // 记录详细的错误信息但不影响服务初始化
        if (error instanceof Error) {
          logging.warn('WebSocket URL获取失败详情:', {
            message: error.message,
            name: error.name
          });
        }
      }
      
      // 使用 proxyMCPClient 连接到代理服务器和远程 WebSocket（如果可用）
      await proxyMCPClient.connect(mergedServers, websocketUrl || undefined);

      this.isLoaded = true;
      this.isLoading = false;
      console.log('✅ MCP Service load completed successfully');
    } catch (error) {
      logging.error('❌ MCP Service load failed:', error);
      // 即使失败也标记为已加载，防止重复尝试
      this.isLoaded = false;
      this.isLoading = false;
      throw error;
    } finally {
      // 确保在任何情况下都重置 isLoading 状态
      this.isLoading = false;
    }
  }

  /**
   * 断开MCP服务连接
   */
  public async disconnect(): Promise<void> {
    try {
      logging.info('正在断开MCP服务连接...');
      
      // 断开proxyMCPClient连接（这会禁用重连）
      await proxyMCPClient.disconnect();
      
      // 重置加载状态，允许重新初始化
      this.isLoaded = false;
      this.isLoading = false;
      
      logging.info('✅ MCP服务连接已断开');
    } catch (error) {
      logging.error('断开MCP服务连接时出错:', error);
      // 即使出错也重置状态
      this.isLoaded = false;
      this.isLoading = false;
    }
  }

  public async addServer(key: string, server: MCPServer) {
    const config = await this.getConfig();
    if (!config.servers[key]) {
      config.servers[key] = server;
      await this.putConfig(config);
      return true;
    }
    return false;
  }

  public async updateServer(key: string, server: MCPServer) {
    const config = await this.getConfig();
    if (config.servers[key]) {
      config.servers[key] = server;
      await this.putConfig(config);
      return true;
    }
    return false;
  }

  /**
   * 使用 bun install 安装包
   * @param packageName 包名
   * @param bunPath bun 可执行文件路径
   * @param env 环境变量
   */
  private async bunInstall(packageName: string, bunPath: string, env: Record<string, string>): Promise<void> {
    try {
      // 1. 先检查包是否已经安装
      try {
        const checkCmd = `${bunPath} pm ls -g ${packageName.split('@')[0]}`;
        execSync(checkCmd, { env, stdio: 'pipe', timeout: 5000 });
        logging.info(`包已存在，跳过安装: ${packageName}`);
        return;
      } catch {
        // 包不存在，继续安装
      }
      
      const installArgs = [
        'install',
        '--global',
        '--registry', 'https://registry.npmmirror.com',
        '--silent', // 减少输出
        packageName
      ];
      
      const installCmd = `${bunPath} ${installArgs.join(' ')}`;
      logging.info(`执行安装命令: ${installCmd}`);
      
      // 2. 使用异步执行，避免阻塞
      await new Promise<void>((resolve, reject) => {
        const { spawn } = require('child_process');
        const child = spawn(bunPath, installArgs, {
          env,
          stdio: 'pipe',
          timeout: 120000, // 增加到120秒
        });
        
        let output = '';
        child.stdout?.on('data', (data: Buffer) => {
          output += data.toString();
        });
        
        child.stderr?.on('data', (data: Buffer) => {
          output += data.toString();
        });
        
        child.on('close', (code: number) => {
          if (code === 0) {
            logging.info(`包安装成功: ${packageName}`);
            resolve();
          } else {
            logging.warn(`包安装失败: ${packageName}, 退出码: ${code}, 输出: ${output}`);
            resolve(); // 不抛出错误，继续执行
          }
        });
        
        child.on('error', (error: { message: any; }) => {
          logging.warn(`包安装出错: ${packageName}, 错误: ${error.message}`);
          resolve(); // 不抛出错误，继续执行
        });
      });
      
    } catch (error: any) {
      logging.warn(`包安装失败: ${packageName}, 错误: ${error.message}`);
      // 不抛出错误，继续执行
    }
  }

  public async activate(key: string, server: MCPServer): Promise<Transport> {
    try {
      const config = await this.getConfig();
      const mcpSvr = ModuleContext.getMCPServer(key, server, config) as MCPServer;
      const { command, args, env } = mcpSvr;
      let cmd: string = command;
      let finalArgs = args;
      
      const mergedEnv = {
        ...getDefaultEnvironment(),
        ...env,
        PATH: process.env.PATH,
        BUN_INSTALL_CACHE_DIR: path.join(USER_DATA_PATH, '.bun-global-cache')
      };
      
      // 使用内置的bun替换npx
      if (command === 'npx' || command === "node") {
        const binDir = getBinDir();
        const bunExecutableName = getBunName();
        cmd = path.join(binDir, bunExecutableName);
        
        // 将npx的参数转换为bunx的参数
        // npx ['-y', '@mcpcn/mcp-xxx'] -> bunx '@mcpcn/mcp-xxx'
        if (args && args.length > 0) {
          // 过滤掉 -y 参数，保留包名，使用bunx代替npx
          let filteredArgs = args.filter((arg: string) => arg !== '-y');
          
          // 提取包名并添加@latest
          const packageName = filteredArgs[0];
          // 过滤掉包名
          filteredArgs = filteredArgs.filter((arg: string) => arg !== packageName);

          const latestPackageName = packageName.endsWith("@latest") ? packageName : packageName+"@latest"

          await this.bunInstall(latestPackageName, cmd, mergedEnv);
          
          //使用淘宝源
          finalArgs = ['x',latestPackageName,...filteredArgs];
        }
        
        logging.info(`使用内置bun替换npx: ${cmd} ${finalArgs.join(' ')}`);
      }

      if(command === 'python' || command ==='uv' || command ==='uvx') {
        const binDir = getBinDir();
        const uvxName = getUvxName();
        cmd = path.join(binDir, uvxName);
        
        //使用清华源
        finalArgs = ['--index-url', 'https://pypi.tuna.tsinghua.edu.cn/simple/', ...finalArgs];
        
        logging.info(`使用内置uvx替换python或uv: ${cmd} ${finalArgs.join(' ')}`);
      }
      
      // 只创建transport，不连接
      const transport = new StdioClientTransport({
        command: cmd,
        args: finalArgs,
        stderr: process.platform === 'win32' ? 'pipe' : 'inherit',
        env: mergedEnv,
      });
      
      // 更新配置（标记为激活）
      // const updatedServer = { ...mcpSvr, isActive: true };
      // await this.updateConfigAfterActivation(key, updatedServer, config);
      return transport;
    } catch (error: any) {
      logging.captureException(error);
      logging.debug('Activating server:', key);
      this.deactivate(key);
      throw error;
    }
  }

  public async deactivate(key: string) {
    try {
      if (this.clients[key]) {
        await this.clients[key].close();
        delete this.clients[key];
      }
      await this.updateConfigAfterDeactivation(key, await this.getConfig());
      return { error: null };
    } catch (error: any) {
      logging.captureException(error);
      return { error };
    }
  }

  public async close() {
    await Promise.all(
      Object.keys(this.clients).map(async (key) => {
        logging.info(`Closing MCP Client ${key}`);
        await this.clients[key].close();
        delete this.clients[key];
      }),
    );
  }

  public async listTools(key?: string): Promise<any[]> {
    try {
      // 使用本地代理客户端列出技能
      return await proxyMCPClient.listTools();
    } catch (error) {
      logging.error('listTools error:', error);
      return [];
    }
  }

  public async callTool({
    name,
    args,
  }: {
    name: string;
    args: any;
  }) {
    try {
      // 使用本地代理客户端调用技能
      return await proxyMCPClient.callTool(name, args);
    } catch (error) {
      logging.error('callTool error:', error);
      throw error;
    }
  }

  public getClient(name: string) {
    return this.clients[name];
  }

  public getClientNames() {
    return Object.keys(this.clients);
  }

  // 数据库相关的MCP服务器管理方法
  public async addMcpServerToDb(id: string, server: MCPServer, isAido:boolean): Promise<boolean> {
    try {
      dbService.deleteMcpServer(id)//删除旧的
      dbService.saveMcpServer(id, server,isAido);
      return true;
    } catch (error) {
      logging.error(`保存MCP服务器到数据库失败: ${id}`, error);
      return false;
    }
  }

  public async deleteMcpServerFromDb(id: string): Promise<boolean> {
    try {
      const success = dbService.deleteMcpServer(id);
      if (success) {
        logging.info(`MCP服务器已删除: ${id}`);
      } else {
        logging.warn(`MCP服务器不存在: ${id}`);
      }
      return success;
    } catch (error) {
      logging.error(`删除MCP服务器失败: ${id}`, error);
      return false;
    }
  }

  public getMcpServerFromDb(id: string): MCPServer | null {
    try {
      return dbService.getMcpServer(id);
    } catch (error) {
      logging.error(`获取MCP服务器失败: ${id}`, error);
      return null;
    }
  }

  public getAllMcpServersFromDb(): Record<string, MCPServer> {
    try {
      return dbService.getAllMcpServers();
    } catch (error) {
      logging.error('获取所有MCP服务器失败', error);
      return {};
    }
  }

  // 重新加载配置（合并文件和数据库配置）
  public async reload() {
    this.isLoaded = false;
    this.isLoading = false;
    await this.load();
  }

  /**
   * 动态加载单个MCP服务器，不影响现有连接
   * @param key 服务器键名
   * @param server 服务器配置
   */
  public async loadSingleServer(key: string, server: MCPServer): Promise<boolean> {
    try {
      // 检查 proxyMCPClient 是否已连接
      if (!proxyMCPClient.getConnectionStatus()) {
        logging.warn('ProxyMCPClient 未连接，尝试重新初始化连接');
        
        // 尝试重新加载MCP服务以建立连接
        try {
          await this.reload();
          
          // 重新检查连接状态
          if (!proxyMCPClient.getConnectionStatus()) {
            logging.error('重新初始化连接失败，无法添加单个服务器');
            return false;
          }
          
          logging.info('ProxyMCPClient 连接已恢复');
        } catch (reloadError) {
          logging.error('重新加载MCP服务失败:', reloadError);
          return false;
        }
      }
  
      // 使用 proxyMCPClient 动态添加服务器
      await proxyMCPClient.addSingleServer(key, server);
  
      logging.info(`成功动态加载MCP服务器: ${key} (${server.name})`);
      return true;
    } catch (error) {
      logging.error(`动态加载MCP服务器失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 动态移除单个MCP服务器
   * @param key 服务器键名
   */
  public async unloadSingleServer(key: string): Promise<boolean> {
    try {
      // 检查 proxyMCPClient 是否已连接
      if (!proxyMCPClient.getConnectionStatus()) {
        logging.warn('ProxyMCPClient 未连接，无法移除单个服务器');
        return false;
      }

      logging.info(`开始动态移除MCP服务器: ${key}`);

      // 使用 proxyMCPClient 动态移除服务器
      await proxyMCPClient.removeSingleServer(key);
      
      logging.info(`成功动态移除MCP服务器: ${key}`);
      return true;
    } catch (error) {
      logging.error(`动态移除MCP服务器失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 检查MCP服务是否已加载
   */
  public isServiceLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 根据ID获取MCP工具
   * @param id 工具ID
   * @returns 返回匹配的MCP工具数组
   */
  public getMcpToolById(id: string): MCPTool[] {
    try {
      return dbService.getMcpServerToolById(id);
    } catch (error) {
      logging.error(`获取MCP工具失败: ${id}`, error);
      return [];
    }
  }

  /**
   * 重新连接WebSocket（用于用户登录成功后）
   * 保持本地连接不变，只重新建立WebSocket连接
   */
  public async reconnectWebSocket(): Promise<void> {
    try {
      logging.info('用户登录成功，尝试重新连接MCP WebSocket...');
      
      // 检查proxyMCPClient是否已连接
      if (!proxyMCPClient.getConnectionStatus()) {
        logging.warn('ProxyMCPClient未连接，无法重新连接WebSocket');
        return;
      }
      
      // 尝试获取新的WebSocket URL
      let websocketUrl: string | null = null;
      try {
        websocketUrl = await this.getMcpWebSocketUrl();
        logging.info(`获取到新的WebSocket URL: ${websocketUrl}`);
      } catch (error) {
        logging.warn('获取WebSocket URL失败，跳过WebSocket重连:', error);
        return;
      }
      
      if (!websocketUrl) {
        logging.warn('未获取到有效的WebSocket URL，跳过重连');
        return;
      }
      
      // 读取当前的服务器配置
      const { servers: fileServers } = await this.getConfig();
      const dbServers = dbService.getAllMcpServers();
      const mergedServers = { ...fileServers, ...dbServers };
      
      // 只重新连接WebSocket部分，不重置本地连接状态
      logging.info('重新连接WebSocket部分...');
      
      // 调用proxyMCPClient的WebSocket重连方法
      await proxyMCPClient.reconnectWebSocketOnly(mergedServers, websocketUrl);
      
      logging.info('✅ MCP WebSocket重连成功');
    } catch (error) {
      logging.error('MCP WebSocket重连失败:', error);
      // 不抛出错误，因为本地连接仍然可用
    }
  }
}

// 导出单例实例
export const mcpService = new ModuleContext();