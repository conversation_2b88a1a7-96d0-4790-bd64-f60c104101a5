import { app , screen , nativeImage} from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import appSearch from '../../core/app-search';
export interface AppInfo {
  id: string;
  name: string;
  path: string;
  icon?: string; // 图标路径或Base64编码的图标
  lastUsed?: number; // 最后使用时间戳
  type: 'app'; // 类型为应用
}

/**
 * 应用管理服务
 * 负责扫描、搜索、启动和管理应用
 */
class AppManager {
  private allApps: AppInfo[] = [];
  constructor() {
    // 绑定方法
    this.launchApp = this.launchApp.bind(this);
  }

  // 启动应用
  public async launchApp(appPath: string): Promise<boolean> {
    try {
      if (process.platform === 'darwin') {
        // 使用open命令打开macOS应用
        exec(`open "${appPath}"`);
      } else if (process.platform === 'win32') {
        // 使用start命令打开Windows应用
        exec(`start "" "${appPath}"`);
      } else if (process.platform === 'linux') {
        // 使用xdg-open命令打开Linux应用
        exec(`xdg-open "${appPath}"`);
      }

      return true;
    } catch (error) {
      console.error(`启动应用 ${appPath} 出错:`, error);
      return false;
    }
  }
  // 根据使用频率排序
  // private sortAppsByUsage(allApps: AppInfo[]): void {
  //   allApps.sort((a, b) => {
  //     // 首先比较最后使用时间
  //     const aLastUsed = a.lastUsed || 0;
  //     const bLastUsed = b.lastUsed || 0;

  //     if (aLastUsed > bLastUsed) return -1;
  //     if (aLastUsed < bLastUsed) return 1;

  //     // 如果最后使用时间相同，则按照名称排序
  //     return a.name.localeCompare(b.name);
  //   });
  // }
  
  // 获取应用列表
  public async getAppList(): Promise<AppInfo[]> {
    try {
      this.allApps = await appSearch(nativeImage);
      // 应用使用频率排序
      // this.sortAppsByUsage(this.allApps);
      return this.allApps;
    } catch (error) {
      console.error('获取应用列表失败:', error);
      return [];
    }
  }
}

// 导出单例实例
export const appService = new AppManager();