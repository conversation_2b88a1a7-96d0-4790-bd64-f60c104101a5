import { BrowserWindow, screen, ipcMain, app , clipboard, nativeImage } from 'electron';
import path from 'node:path';
import { format as formatUrl } from 'url';
import { isDevelopment, WINDOW_CONFIG, isMac, APP_PATH} from '../common';
import { WindowManager } from './window-manager';
import * as log from '../logging';
import { FLOATING_BALL, SCREEN_CAPTURE, VOICE_INPUT } from '../../shared/ipc';

/**
 * 悬浮球窗口类
 * 管理悬浮球窗口的创建、位置、拖动等
 */
export class FloatingBall {
  private window: BrowserWindow | null = null;
  
  // 记录拖动开始时的窗口位置和鼠标位置
  private dragData = {
    startMouseX: 0,
    startMouseY: 0,
    startWindowX: 0,
    startWindowY: 0,
    isDragging: false
  };
  
  constructor() {
    this.createWindow();
    this.registerIpcHandlers();
  }
  
  /**
   * 创建悬浮球窗口
   */
  private createWindow(): void {
    // 创建无边框、透明背景的窗口
    this.window = new BrowserWindow({
      width: WINDOW_CONFIG.floatingBall.width,
      height: WINDOW_CONFIG.floatingBall.height,
      transparent: true,
      frame: false,
      resizable: isDevelopment,
      fullscreenable: false,
      show: true,
      alwaysOnTop: isDevelopment,
      skipTaskbar: !isDevelopment,
      focusable: false,
      hasShadow: false,
      webPreferences: {
        preload: path.join(APP_PATH, '.vite/build/preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        devTools: isDevelopment,
        backgroundThrottling: false
      },
      // 确保窗口背景透明
      backgroundColor: '#00000000'
    });
    
    // 确保窗口完全透明
    this.window.setBackgroundColor('#00000000');
    
    // 设置窗口不能最大化
    this.window.setMaximizable(false);
    
    // 设置鼠标事件穿透，但允许特定区域（悬浮球本身）接收点击
    this.window.setIgnoreMouseEvents(true, { forward: true });
    
    // 设置悬浮球位置，吸附在屏幕右侧中间位置
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;
    const snapX = width - WINDOW_CONFIG.floatingBall.width;
    const snapY = Math.floor(height / 2) - Math.floor(WINDOW_CONFIG.floatingBall.height / 2);
    this.window.setPosition(snapX, snapY);
    
    // 确保悬浮球窗口总是置顶
    this.window.setAlwaysOnTop(true, 'floating', 1);
    
    // 不显示在任务栏和Dock上
    this.window.setSkipTaskbar(true);
    
    // macOS 特有的设置
    if (isMac) {
      app.dock.hide(); // 隐藏dock图标
    }
    
    this.loadContent();
    // this.setupEventListeners();
    
    // 仅在开发环境下自动打开开发者工具
    // if (isDevelopment) {
    //   // 打开开发者工具
    //   this.window.webContents.openDevTools({ mode: 'detach' });
    // }
    

  }
  
  /**
   * 加载窗口内容
   */
  private loadContent(): void {
    if (!this.window) return;
    
    if (isDevelopment) {
      // 添加重试机制，确保Vite服务器已启动
      const tryLoadURL = (retryCount = 0, maxRetries = 10) => {
        this.window?.loadURL('http://localhost:5173/#/floating-ball')
          .then(() => {

          })
          .catch(err => {
            log.error(`悬浮球连接Vite服务器失败(${retryCount}): ${err.message}`);
            if (retryCount < maxRetries) {

              setTimeout(() => tryLoadURL(retryCount + 1), 1000);
            } else {
              log.error('悬浮球达到最大重试次数，无法连接Vite服务器');
            }
          });
      };
      
      tryLoadURL();
    } else {
      const htmlPath = path.join(APP_PATH, '.vite/renderer/main_window/index.html');
      this.window.loadURL(formatUrl({
        pathname: htmlPath,
        protocol: 'file:',
        slashes: true,
        hash: '/floating-ball'
      }));
    }
  }
  
  /**
   * 注册 IPC 事件处理程序
   */
  private registerIpcHandlers(): void {
    // 处理旧的拖拽方法 (兼容性)
    ipcMain.on(FLOATING_BALL.DRAG, (_, { x, y }) => {
      if (this.window && !this.window.isDestroyed()) {
        const currentPosition = this.window.getPosition();
        this.window.setPosition(currentPosition[0] + x, currentPosition[1] + y);
      }
    });
    
    // 控制鼠标事件穿透
    ipcMain.on(FLOATING_BALL.SET_IGNORE_MOUSE, (_, { ignore, x, y, width, height }) => {
      if (this.window && !this.window.isDestroyed()) {

        
        if (ignore) {
          // 启用鼠标事件穿透，但允许区域接收点击
          this.window.setIgnoreMouseEvents(true, { forward: true });
        } else {
          // 禁用鼠标事件穿透，整个窗口可以接收鼠标事件
          this.window.setIgnoreMouseEvents(false);
        }
      }
    });
    
    // 开始拖动时记录初始状态
    ipcMain.on(FLOATING_BALL.DRAG_START, (_, mousePosition) => {
      if (!this.window || this.window.isDestroyed()) return;
      
      const [windowX, windowY] = this.window.getPosition();
      
      this.dragData = {
        startMouseX: mousePosition.x,
        startMouseY: mousePosition.y,
        startWindowX: windowX,
        startWindowY: windowY,
        isDragging: true
      };
      

      
      // 告知渲染进程拖动已开始
      this.window.webContents.send(FLOATING_BALL.DRAGGING, true);
    });
    
    // 拖动过程中计算和设置新位置
    ipcMain.on(FLOATING_BALL.DRAG_MOVE, (_, mousePosition) => {
      if (!this.window || this.window.isDestroyed() || !this.dragData.isDragging) return;
      
      // 计算鼠标移动的距离
      const deltaX = mousePosition.x - this.dragData.startMouseX;
      const deltaY = mousePosition.y - this.dragData.startMouseY;
      
      // 基于窗口的初始位置和鼠标移动距离计算新位置
      const newX = this.dragData.startWindowX + deltaX;
      const newY = this.dragData.startWindowY + deltaY;
      
      // 设置新位置
      this.window.setPosition(Math.round(newX), Math.round(newY));
    });
    
    // 拖动结束
    ipcMain.on(FLOATING_BALL.DRAG_END, () => {
      if (!this.window || this.window.isDestroyed()) return;
      
      this.dragData.isDragging = false;
      
      const [currentX, currentY] = this.window.getPosition();

      
      // 自动吸附到当前屏幕的右侧
      this.snapToScreenEdge(currentX, currentY);
      
      // 告知渲染进程拖动已结束
      this.window.webContents.send(FLOATING_BALL.DRAGGING, false);
    });
    
    // 双击事件
    ipcMain.on(FLOATING_BALL.DOUBLE_CLICK, () => {
      if (!this.window || this.window.isDestroyed()) return;

      
      // 双击启动截图模式
      if (this.window && !this.window.isDestroyed()) {
        // 先隐藏悬浮球窗口
        this.window.hide();
        
        // 使用WindowManager启动截图
        WindowManager.startScreenCapture();
        this.window.webContents.send(SCREEN_CAPTURE.CHANGE, true);
        

      }
    });
    
    // 长按事件
    ipcMain.on(FLOATING_BALL.LONG_PRESS, () => {
      if (!this.window || this.window.isDestroyed()) return;

      
      // 触发语音输入开始
      this.window.webContents.send(VOICE_INPUT.CHANGE, true, 0);
    });

    // 处理语音输入开始
    ipcMain.on(VOICE_INPUT.START, () => {
      if (!this.window || this.window.isDestroyed()) return;

      
      // 通知渲染进程语音输入状态变更
      this.window.webContents.send(VOICE_INPUT.CHANGE, true, 0);
    });
    
    // 处理语音输入结束
    ipcMain.on(VOICE_INPUT.END, (_,voiceBase64:ArrayBuffer) => {
      if (!this.window || this.window.isDestroyed()) return;

      
      // 通知渲染进程语音输入状态变更
      this.window.webContents.send(VOICE_INPUT.CHANGE, false);
    });

    // 处理开始截图
    ipcMain.on(SCREEN_CAPTURE.START, (_, position) => {

    });
    
    // 处理结束截图 - 只负责接收通知并更新UI状态
    ipcMain.on(SCREEN_CAPTURE.END, (_, base64) => {
      const image = nativeImage.createFromDataURL(base64);
      clipboard.writeImage(image); // 写入剪切板
      // 关闭截图窗口
      WindowManager.closeScreenCaptureWindow();
      // 截图完成后显示悬浮球
      if (this.window && !this.window.isDestroyed()) {
        this.window.show();
        // 通知渲染进程退出截图模式
        this.window.webContents.send(SCREEN_CAPTURE.CHANGE, false);
      }
    });
    
    // 处理取消截图
    ipcMain.on(SCREEN_CAPTURE.CANCEL, () => {

      
      // 截图取消后显示悬浮球
      if (this.window && !this.window.isDestroyed()) {
        this.window.show();
        // 通知渲染进程退出截图模式
        this.window.webContents.send(SCREEN_CAPTURE.CHANGE, false);
      }
      // 关闭截图窗口
      WindowManager.closeScreenCaptureWindow();
    });
  }
  
  /**
   * 显示悬浮球
   */
  public show(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.show();
    } else {
      this.createWindow();
    }
  }
  
  /**
   * 隐藏悬浮球
   */
  public hide(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide();
    }
  }
  
  /**
   * 自动吸附到屏幕边缘
   * @param currentX 当前X坐标
   * @param currentY 当前Y坐标
   */
  private snapToScreenEdge(currentX: number, currentY: number): void {
    if (!this.window || this.window.isDestroyed()) return;
    
    // 获取悬浮球尺寸
    const { width: windowWidth, height: windowHeight } = this.window.getBounds();

    // 通过窗口矩形选择与其重叠面积最大的显示器，避免仅靠中心点在边界时的误判
    const ballBounds = {
      x: currentX,
      y: currentY,
      width: windowWidth,
      height: windowHeight,
    } as const;

    const targetDisplay = screen.getDisplayMatching(ballBounds);

    const { x: displayX, y: displayY, width: displayWidth, height: displayHeight } = targetDisplay.workArea;

    // 计算悬浮球中心点在目标显示器中的位置
    const ballCenterX = currentX + windowWidth / 2;

    // 距离显示器左右边缘的水平距离
    const distToLeft = Math.abs(ballCenterX - displayX);
    const distToRight = Math.abs(displayX + displayWidth - ballCenterX);

    let snapSide: 'left' | 'right';
    let snapX: number;

    if (distToLeft <= distToRight) {
      // 吸附到左侧
      snapX = displayX;
      snapSide = 'left';
    } else {
      // 吸附到右侧
      snapX = displayX + displayWidth - WINDOW_CONFIG.floatingBall.width;
      snapSide = 'right';
    }
    
    // Y轴：保持当前位置，但确保在屏幕范围内
    let snapY = currentY;
    const minY = displayY + 10;
    const maxY = displayY + displayHeight - WINDOW_CONFIG.floatingBall.height - 10;
    
    // 限制Y坐标在屏幕范围内
    if (snapY < minY) {
      snapY = minY;
    } else if (snapY > maxY) {
      snapY = maxY;
    }
    
    log.debug('悬浮球自动吸附', {
      from: [currentX, currentY],
      to: [snapX, snapY],
      side: snapSide,
      display: { x: displayX, y: displayY, width: displayWidth, height: displayHeight }
    });
    
    // 设置新位置
    this.window.setPosition(Math.round(snapX), Math.round(snapY));

    // 无论结果是否改变，都向渲染进程广播吸附侧，确保 UI 状态同步
    this.window.webContents.send(FLOATING_BALL.SNAP_SIDE_CHANGE, snapSide);
  }

  /**
   * 切换悬浮球显示状态
   */
  public toggle(): void {
    if (!this.window) {
      this.createWindow();
      return;
    }
    
    if (this.window.isVisible()) {
      this.hide();
    } else {
      this.show();
    }
  }
  /**
   * 切换窗口开发者工具
   */
  public toggleDevTools(): void {
    if (!isDevelopment || !this.window || this.window.isDestroyed()) return;

    const devToolsOpened = this.window.webContents.isDevToolsOpened();
    if (devToolsOpened) {
      this.window.webContents.closeDevTools();
    } else {
      // 确保窗口可见
      if (!this.window.isVisible()) {
        this.show();
      }
      this.window.webContents.openDevTools({ mode: 'detach' });
    }
  }
  /**
   * 检查悬浮球是否可见
   */
  public isVisible(): boolean {
    return this.window ? this.window.isVisible() : false;
  }
  
  /**
   * 获取悬浮球窗口实例
   */
  public getWindow(): BrowserWindow | null {
    return this.window;
  }

  /**
   * 检查窗口是否已销毁
   */
  public isDestroyed(): boolean {
    return !this.window || this.window.isDestroyed();
  }

  /**
   * 销毁窗口
   */
  public destroy(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy();
      this.window = null;
    }
  }
} 