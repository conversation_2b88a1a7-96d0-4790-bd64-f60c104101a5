import { BrowserWindow, screen, shell, app } from 'electron';
import path from 'node:path';
import { APP_PATH, isDevelopment, WINDOW_CONFIG } from '../common';
import { settingsService } from '../services/settingsService';
import { ClipboardWindowStyle } from '../../shared/types/';
import * as log from '../logging';
import { FocusManager } from '../utils/focusManager';
import { AppSettings } from '@shared/types/settings';

/**
 * 剪贴板历史窗口类
 * 支持两种样式：底部栏和中央窗口
 */
export class ClipboardHistoryWindow {
  private window: BrowserWindow | null = null;
  private currentStyle: ClipboardWindowStyle;
  private previousFocusedApp: string | null = null; // 记住之前的应用
  private isStyleChanging: boolean = false; // 是否正在切换样式
  private settings: AppSettings = settingsService.getSettings();
  constructor(style: ClipboardWindowStyle = 'bottom-bar') {
    this.currentStyle = style;
    this.createWindow();
  }


  /**
   * 恢复之前的应用焦点
   */
  public async restorePreviousFocus(): Promise<void> {
    if (!this.previousFocusedApp) {
      return;
    }

    try {
      const platform = process.platform;
      const { spawn } = require('child_process');
      
      // 先隐藏当前窗口
      if (this.window && !this.window.isDestroyed()) {
        this.window.hide();
      }
      
      // 增加延迟确保窗口隐藏完成
      await new Promise(resolve => setTimeout(resolve, 200));
      
      if (platform === 'darwin') {
        // macOS: 激活之前的应用，并等待激活完成
        const activatePromise = new Promise<boolean>((resolve) => {
          const child = spawn('osascript', ['-e', `tell application "${this.previousFocusedApp}" to activate`], {
            stdio: 'pipe',
            detached: false
          });
          
          child.on('close', (code: number | null) => {
            resolve(code === 0);
          });
          
          child.on('error', () => {
            resolve(false);
          });
          
          // 设置超时
          setTimeout(() => {
            child.kill();
            resolve(false);
          }, 3000);
        });
        
        const success = await activatePromise;
        if (success) {
          // 再等待一下确保应用真正获得焦点
          await new Promise(resolve => setTimeout(resolve, 200));
          log.debug('成功恢复焦点到:', this.previousFocusedApp);
        } else {
          log.warn('激活应用失败:', this.previousFocusedApp);
        }
      } else if (platform === 'win32') {
        // Windows: 激活之前的应用窗口
        const script = `
          $processes = Get-Process -Name "${this.previousFocusedApp}" -ErrorAction SilentlyContinue
          if ($processes.Count -gt 0) {
            $process = $processes[0]
            if ($process.MainWindowHandle -ne [System.IntPtr]::Zero) {
              Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport("user32.dll")] public static extern bool SetForegroundWindow(IntPtr hWnd); }'
              [Win32]::SetForegroundWindow($process.MainWindowHandle)
            }
          }
        `;
        spawn('powershell', ['-Command', script], {
          stdio: 'ignore',
          detached: true,
          windowsHide: true
        });
        
        // 等待Windows焦点切换完成
        await new Promise(resolve => setTimeout(resolve, 300));
      } else {
        // Linux: 尝试恢复焦点
        spawn('wmctrl', ['-a', this.previousFocusedApp], {
          stdio: 'ignore',
          detached: true
        });
        
        // 等待Linux焦点切换完成
        await new Promise(resolve => setTimeout(resolve, 300));
      }
      
      log.debug('尝试恢复焦点到:', this.previousFocusedApp);
    } catch (error) {
      log.error('恢复焦点失败:', error);
    }
  }

  /**
   * 超快速恢复之前的应用焦点（用于粘贴操作）
   */
  public async restorePreviousFocusFast(): Promise<void> {
    if (!this.previousFocusedApp) {
      log.warn('快速焦点恢复失败: 没有记录的焦点应用');
      return;
    }

    log.debug('开始快速焦点恢复，目标应用:', this.previousFocusedApp);
    
    try {
      const platform = process.platform;
      const { spawn } = require('child_process');
      
      if (platform === 'darwin') {
        // macOS: 快速激活应用，不等待太久
        return new Promise<void>((resolve) => {
          // 尝试从FocusManager获取Bundle ID信息
          const bundleId = FocusManager.getPreviousFocusedBundleId();
          let activateScript = '';

          if (bundleId && this.previousFocusedApp) {
            // 优先使用Bundle ID激活
            activateScript = `tell application id "${bundleId}" to activate`;
            log.debug('剪切板窗口使用Bundle ID快速激活:', { app: this.previousFocusedApp, bundleId });
          } else {
            // 回退到应用名称激活
            activateScript = `tell application "${this.previousFocusedApp}" to activate`;
            log.debug('剪切板窗口使用应用名称快速激活:', this.previousFocusedApp);
          }

          const child = spawn('osascript', ['-e', activateScript], {
            stdio: 'pipe',
            detached: false
          });
          
          let output = '';
          let errorOutput = '';
          
          child.stdout?.on('data', (data: Buffer) => {
            output += data.toString();
          });
          
          child.stderr?.on('data', (data: Buffer) => {
            errorOutput += data.toString();
          });
          
          child.on('close', (code: number | null) => {
            if (code === 0) {
              log.debug('macOS快速焦点恢复成功');
            } else {
              log.warn(`macOS快速焦点恢复警告，退出代码: ${code}, 错误: ${errorOutput}`);
            }
            resolve();
          });
          
          child.on('error', (error: Error) => {
            log.warn('macOS快速焦点恢复进程错误:', error.message);
            resolve();
          });
          
          // 快速超时，只等500ms
          setTimeout(() => {
            child.kill();
            log.warn('macOS快速焦点恢复超时');
            resolve();
          }, 500);
        });
      } else if (platform === 'win32') {
        // Windows: 快速激活
        const script = `
          $processes = Get-Process -Name "${this.previousFocusedApp}" -ErrorAction SilentlyContinue
          if ($processes.Count -gt 0) {
            $process = $processes[0]
            if ($process.MainWindowHandle -ne [System.IntPtr]::Zero) {
              Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport("user32.dll")] public static extern bool SetForegroundWindow(IntPtr hWnd); }'
              [Win32]::SetForegroundWindow($process.MainWindowHandle)
              Write-Output "Success"
            } else {
              Write-Output "NoWindow"
            }
          } else {
            Write-Output "NotFound"
          }
        `;
        
        return new Promise<void>((resolve) => {
          const child = spawn('powershell', ['-Command', script], {
            stdio: 'pipe',
            windowsHide: true
          });
          
          let output = '';
          child.stdout?.on('data', (data: Buffer) => {
            output += data.toString();
          });
          
          child.on('close', () => {
            const result = output.trim();
            if (result === 'Success') {
              log.debug('Windows快速焦点恢复成功');
            } else {
              log.warn(`Windows快速焦点恢复结果: ${result}`);
            }
            resolve();
          });
          
          child.on('error', (error: Error) => {
            log.warn('Windows快速焦点恢复错误:', error.message);
            resolve();
          });
        });
      } else {
        // Linux: 快速恢复焦点
        return new Promise<void>((resolve) => {
          const child = spawn('wmctrl', ['-a', this.previousFocusedApp], {
            stdio: 'pipe'
          });
          
          child.on('close', (code: number | null) => {
            if (code === 0) {
              log.debug('Linux快速焦点恢复成功');
            } else {
              log.warn(`Linux快速焦点恢复失败，退出代码: ${code}`);
            }
            resolve();
          });
          
          child.on('error', (error: Error) => {
            log.warn('Linux快速焦点恢复错误:', error.message);
            resolve();
          });
        });
      }
    } catch (error) {
      log.warn('快速焦点恢复异常:', error);
    }
  }

  /**
   * 创建剪贴板历史窗口
   */
  private createWindow(): void {
    const windowBounds = this.calculateFinalWindowBounds();
    
    this.window = new BrowserWindow({
      ...windowBounds,
      transparent: true,
      frame: false,
      resizable: false,
      show: false,
      alwaysOnTop: false,
      skipTaskbar: true,
      focusable: true,
      webPreferences: {
        preload: path.join(APP_PATH, '.vite/build/preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        devTools: isDevelopment,
        webSecurity: !isDevelopment,
      },
    });

    if (process.platform === 'darwin' && this.currentStyle === 'bottom-bar') {
      // 使用更高的层级覆盖 Dock
      this.setBottomBarAlwaysOnTop();
      this.window.setHasShadow(false);
    }

    this.loadContent();
    this.setupEventListeners();

    log.debug(`窗口创建完成: ${JSON.stringify(windowBounds)}`);
  }

  /**
   * 计算窗口位置
   */
  private calculateFinalWindowBounds() {
    if (this.currentStyle === 'bottom-bar') {
      const primaryDisplay = screen.getPrimaryDisplay();
      const { width, height, x: screenX, y: screenY } = primaryDisplay.bounds;
      const windowHeight = 270;
      
      // 确保窗口贴到绝对底部，不留任何空隙
      const windowY = screenY + height - windowHeight;
      
      const bounds = {
        x: screenX,
        y: windowY,
        width: width,
        height: windowHeight,
      };
      
      log.debug(`底部栏窗口位置计算:
        屏幕bounds: {x: ${screenX}, y: ${screenY}, width: ${width}, height: ${height}}
        窗口高度: ${windowHeight}
        计算的Y位置: ${windowY} (屏幕底部 ${screenY + height} - 窗口高度 ${windowHeight})
        最终窗口bounds: ${JSON.stringify(bounds)}`);
      
      return bounds;
    } else {
      const mousePosition = screen.getCursorScreenPoint();
      const currentScreen = screen.getDisplayNearestPoint(mousePosition);
      const { x, y, width, height } = currentScreen.workArea;
      
      const windowWidth = WINDOW_CONFIG.clipboard.centerWindow.width;
      const windowHeight = WINDOW_CONFIG.clipboard.centerWindow.height;
      
      return {
        x: x + Math.floor((width - windowWidth) / 2),
        y: y + Math.floor((height - windowHeight) / 2),
        width: windowWidth,
        height: windowHeight,
      };
    }
  }

  /**
   * 加载窗口内容
   */
  private loadContent(): void {
    if (!this.window) return;

    if (isDevelopment) {
      const tryLoadURL = (retryCount = 0, maxRetries = 10) => {
        this.window?.loadURL('http://localhost:5173/#/clipboard-history')
          .then(() => {
            log.debug('剪贴板历史窗口成功连接到Vite开发服务器');
            if (isDevelopment) {
              this.window?.webContents.openDevTools({ mode: 'detach' });
            }
          })
          .catch(err => {
            log.error(`连接Vite服务器失败(${retryCount}): ${err.message}`);
            if (retryCount < maxRetries) {
              setTimeout(() => tryLoadURL(retryCount + 1), 1000);
            }
          });
      };
      tryLoadURL();
    } else {
      const htmlPath = path.join(APP_PATH, '.vite/renderer/main_window/index.html');
      this.window.loadFile(htmlPath, { hash: '/clipboard-history' }).catch(err => {
        log.error('Failed to load clipboard history HTML:', err);
      });
    }
  }

  /**
   * 设置窗口事件监听器
   */
  private setupEventListeners(): void {
    if (!this.window) return;

    // 窗口失去焦点时自动隐藏
    this.window.on('blur', () => {
      if (this.settings.clipboard.autoHide && !this.isStyleChanging) {
        // 延迟一点点隐藏，避免某些情况下的闪烁
        setTimeout(() => {
          if (this.window && !this.window.isFocused() && !this.isStyleChanging) {
            this.hide();
          }
        }, 100);
      }
    });

    // 防止窗口被关闭，只是隐藏
    this.window.on('close', (event) => {
      event.preventDefault();
      this.hide();
    });

    // 处理外部链接
    this.window.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  /**
   * 显示窗口
   */
  public async show(): Promise<void> {
    // 关键：在显示窗口前先同步记录当前焦点
    try {
      await FocusManager.rememberCurrentFocus();
      log.debug('独立窗口模式：焦点记录完成');
    } catch (error) {
      log.error('独立窗口模式：焦点记录失败', error);
    }

    if (!this.window || this.window.isDestroyed()) {
      this.createWindow();

      // 等待窗口内容加载完成
      if (this.window) {
        await new Promise<void>((resolve) => {
          if (this.window?.webContents.isLoading()) {
            this.window.webContents.once('did-finish-load', () => resolve());
          } else {
            resolve();
          }
        });
      }
    }

    if (this.window) {
      this.window.show();
      this.ensureFocus();
      this.window.webContents.send('clipboard-window-style', this.currentStyle);

      // 对于底部栏样式，显示后立即强制设置位置确保贴底
      if (this.currentStyle === 'bottom-bar') {
        const expectedBounds = this.calculateFinalWindowBounds();
        this.window.setBounds(expectedBounds);

        const actualBounds = this.window.getBounds();

        // 如果还是不对，再次强制设置
        if (Math.abs(actualBounds.y - expectedBounds.y) > 5) {
          log.warn(`位置仍有偏差，再次强制设置`);
          setTimeout(() => {
            if (this.window) {
              this.window.setBounds(expectedBounds);
            }
          }, 50);
        }
      }
    }
  }

  /**
   * 隐藏窗口
   */
  public hide(): void {
    // 必须先检查窗口是否被销毁，否则可能出现错误
    if (this.window && !this.window.isDestroyed() && this.window.isVisible()) {
      // 发送重置事件，让渲染进程重置选中项和滚动位置
      this.window.webContents.send('clipboard-window-reset');

      // 直接隐藏，不恢复焦点，因为在某些场景下（如切换窗口模式）我们不希望恢复焦点
      this.window.hide();
    }
  }

  /**
   * 切换窗口显示状态
   */
  public async toggle(): Promise<void> {
    if (!this.window || this.window.isDestroyed()) {
      await this.show();
      return;
    }

    if (this.window.isVisible()) {
      this.hide();
    } else {
      await this.show();
    }
  }

  /**
   * 更换窗口样式
   */
  public updateStyle(style: ClipboardWindowStyle): void {
    if (this.currentStyle === style) {
      return;
    }
    
    console.log(`开始切换窗口样式: ${this.currentStyle} -> ${style}`); // 调试
    
    // 设置样式切换标志，防止自动隐藏
    this.isStyleChanging = true;
    
    this.currentStyle = style;
    const wasVisible = this.window?.isVisible();
    
    console.log(`窗口切换前可见状态: ${wasVisible}`); // 调试
    
    // 重新创建窗口
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy();
    }
    
    this.createWindow();
    
    if (wasVisible) {
      // 立即显示窗口，并设置一个短暂的延迟来确保窗口获得焦点
      this.show().then(() => {
        console.log(`窗口样式切换完成，新样式: ${style}`); // 调试
        
        // 确保窗口获得焦点，对所有样式都处理
        if (this.window) {
          setTimeout(() => {
            if (this.window && !this.window.isDestroyed()) {
              this.window.focus();
              
              // 对于居中窗口，短暂设置为顶层确保显示
              if (this.currentStyle === 'center-window') {
                this.window.setAlwaysOnTop(true);
                // 短暂设置为顶层，然后恢复
                setTimeout(() => {
                  if (this.window && !this.window.isDestroyed()) {
                    this.window.setAlwaysOnTop(false);
                  }
                }, 100);
              }
              
              // 对于底部栏，确保在 macOS 上正确设置 alwaysOnTop
              if (this.currentStyle === 'bottom-bar' && process.platform === 'darwin') {
                this.setBottomBarAlwaysOnTop();
              }
            }
          }, 50);
        }
        
        // 重置样式切换标志，延迟一点确保窗口稳定
        setTimeout(() => {
          this.isStyleChanging = false;
          console.log('样式切换完成，恢复自动隐藏'); // 调试
          
          // 若为底部栏样式，需要保持置顶覆盖 Dock；其他样式短暂置顶后恢复
          if (this.currentStyle !== 'bottom-bar') {
            setTimeout(() => {
              if (this.window && !this.window.isDestroyed()) {
                this.window.setAlwaysOnTop(false);
              }
            }, 100);
          }
        }, 1000);
      });
    } else {
      // 如果之前不可见，也要重置标志
      setTimeout(() => {
        this.isStyleChanging = false;
      }, 1000);
    }
  }

  /**
   * 获取当前样式
   */
  public getCurrentStyle(): ClipboardWindowStyle {
    return this.currentStyle;
  }

  /**
   * 获取窗口实例
   */
  public getWindow(): BrowserWindow | null {
    return this.window;
  }

  /**
   * 检查窗口是否可见
   */
  public isVisible(): boolean {
    return this.window ? this.window.isVisible() : false;
  }

  /**
   * 检查窗口是否已销毁
   */
  public isDestroyed(): boolean {
    return !this.window || this.window.isDestroyed();
  }

  /**
   * 获取之前的焦点应用名称
   */
  public getPreviousFocusedApp(): string | null {
    return this.previousFocusedApp;
  }

  /**
   * 手动设置之前的焦点应用名称（用于应急情况）
   */
  public setPreviousFocusedApp(appName: string | null): void {
    this.previousFocusedApp = appName;
    log.debug('手动设置焦点应用:', appName);
  }

  /**
   * 切换开发者工具
   */
  public toggleDevTools(): void {
    if (!isDevelopment || !this.window || this.window.isDestroyed()) return;

    const devToolsOpened = this.window.webContents.isDevToolsOpened();
    if (devToolsOpened) {
      this.window.webContents.closeDevTools();
    } else {
      this.window.webContents.openDevTools({ mode: 'detach' });
    }
  }

  /**
   * 销毁窗口
   */
  public destroy(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy();
      this.window = null;
    }
  }

  /**
   * 定位窗口（仅用于center-window样式）
   */
  private positionWindow(): void {
    if (!this.window) return;

    if (this.currentStyle === 'center-window') {
      // center-window - 居中显示
      const mousePosition = screen.getCursorScreenPoint();
      const currentScreen = screen.getDisplayNearestPoint(mousePosition);
      const { x, y, width, height } = currentScreen.workArea;
      
      const windowWidth = WINDOW_CONFIG.clipboard.centerWindow.width;
      const windowHeight = WINDOW_CONFIG.clipboard.centerWindow.height;
      
      this.window.setBounds({
        x: x + Math.floor((width - windowWidth) / 2),
        y: y + Math.floor((height - windowHeight) / 2),
        width: windowWidth,
        height: windowHeight
      });
    }
    // bottom-bar样式已在创建时设置正确位置，无需调整
  }

  /**
   * 强制让窗口获得键盘焦点（跨平台兼容）
   * 通过短暂置顶→聚焦→还原 的方式提高成功率
   */
  private ensureFocus(): void {
    if (!this.window || this.window.isDestroyed()) return;

    try {
      // macOS 在有些场景下 show 后不会自动成为 keyWindow，需要显式激活 App
      if (process.platform === 'darwin') {
        // Electron >= 24 支持 app.focus({ steal: true })
        if ((app as any).focus) {
          (app as any).focus({ steal: true });
        }
      }

      // 临时置顶可以帮助抢到焦点（多平台通用）
      if (this.currentStyle === 'bottom-bar' && process.platform === 'darwin') {
        this.setBottomBarAlwaysOnTop();
      } else {
        this.window.setAlwaysOnTop(true);
      }
      this.window.focus();

      // 若为底部栏样式，需要保持置顶覆盖 Dock；其他样式短暂置顶后恢复
      if (this.currentStyle !== 'bottom-bar') {
        setTimeout(() => {
          if (this.window && !this.window.isDestroyed()) {
            this.window.setAlwaysOnTop(false);
          }
        }, 100);
      }
    } catch (err) {
      log.warn('ensureFocus 失败:', err);
    }
  }

  /**
   * 底部栏窗口置顶到 screen-saver 层，确保覆盖 Dock
   */
  private setBottomBarAlwaysOnTop(): void {
    if (!this.window) return;
    this.window.setAlwaysOnTop(true, 'main-menu');
  }
} 