import { MainWindow } from './main-window';
import { FloatingBall } from './floating-ball';
import { ScreenCaptureWindow } from './screen-capture';
import { MultiScreenCaptureManager } from './multi-screen-capture-manager';
import { multiScreenDetector } from '../utils/multi-screen-detector';
import { ClipboardHistoryWindow } from './clipboard-history-window';
import { SettingsWindow } from './settings-window';
import { unregisterAllShortcuts, registerGlobalShortcuts } from './shortcuts';
import { loadShortcutsFromDatabase, cleanupAppShortcuts } from '../ipc/handlers/shortcut-handler';
import { FLOATING_BALL, WINDOW, THEME, LANGUAGE, CLIPBOARD_SETTINGS, USER_STATUS } from '../../shared/ipc';
import { ipcMain, app,screen } from 'electron';
import { IWindowManager, IWindowManagerStatic } from '../types/window';
import * as log from '../logging';
import { FocusManager } from '../utils/focusManager';
import { PetWindow } from './pet-window';
import { AppSettings, ThemeType, LanguageType } from '@shared/types/settings';
import { settingsService } from '@main/services/settingsService';
import { getCrossWindowService } from '../services/cross-window-communication';
import getWinPosition from '../utils/getWinPosition';
import { AUTH } from '../../shared/ipc';

/**
 * 窗口管理器
 * 负责所有窗口的创建、显示、隐藏和协调
 */
export class WindowManager {
  // 主窗口实例
  private static mainWindow: MainWindow | null = null;
  
  // 悬浮球实例
  private static floatingBall: FloatingBall | null = null;
  
  // 截图窗口实例
  private static screenCaptureWindow: ScreenCaptureWindow | null = null;

  // 多屏截图管理器实例
  private static multiScreenCaptureManager: MultiScreenCaptureManager | null = null;

  // 宠物窗口实例
  private static petWindow: PetWindow | null = null;

  // 剪贴板历史窗口实例
  private static clipboardHistoryWindow: ClipboardHistoryWindow | null = null;

  // 设置窗口实例
  private static settingsWindow: SettingsWindow | null = null;
  
  // 钉住状态
  private static pinned: boolean = false;
  
  // 增加一个标志位，用于防止失焦隐藏和切换显示的冲突
  private static isToggling: boolean = false;

  /**
   * 初始化所有窗口
   */
  public static init(): void {
    try {
      // 创建主窗口
      this.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
      
      // 同步钉住状态与置顶状态（如果当前状态是钉住的）
      if (this.pinned && this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.setAlwaysOnTop(true);
      }
      
      // 创建悬浮球
      this.floatingBall = new FloatingBall();

      // // 创建宠物窗口
      // this.petWindow = new PetWindow();

      // 🔥 创建剪贴板历史窗口时使用最新设置
      const currentSettings = settingsService.getSettings();
      this.clipboardHistoryWindow = new ClipboardHistoryWindow(currentSettings.clipboard.clipboardWindowStyle);
      
      // 由于已经在 registerAllIpcHandlers 中注册了窗口IPC,
      // 因此这里只需注册浮动球点击事件即可
      this.registerFloatingBallClickIpc();
      
      // 注册系统快捷键（主窗口、剪贴板）
      registerGlobalShortcuts(this as unknown as IWindowManagerStatic);
      
      // 延迟加载应用快捷键，确保在系统快捷键注册完成后
      setTimeout(() => {
        loadShortcutsFromDatabase().catch(error => {
          log.error('加载应用快捷键失败:', error);
        });
      }, 200); // 给系统快捷键足够的注册时间

      // 延迟初始化跨窗口通信服务的窗口映射，避免循环依赖
      setTimeout(() => {
        try {
          getCrossWindowService().updateWindowMap();
        } catch (error) {
          log.error('更新跨窗口通信服务窗口映射失败:', error);
        }
      }, 100);

      // 注册悬浮球显示/隐藏事件
      ipcMain.on(FLOATING_BALL.SHOW, () => {
        WindowManager.getFloatingBall()?.show();
      });
      ipcMain.on(FLOATING_BALL.HIDE, () => {
        WindowManager.getFloatingBall()?.hide();
      });
    } catch (error) {
      log.error('窗口管理器初始化失败:', error);
    }
  }
  
  /**
   * 注册浮动球点击事件处理程序
   */
  private static registerFloatingBallClickIpc(): void {
    // 点击悬浮球时打开主窗口
    ipcMain.on(FLOATING_BALL.CLICK, () => {
      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        this.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);

        // 同步钉住状态与置顶状态
        if (this.pinned && this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.setAlwaysOnTop(true);
        }

        // 更新跨窗口通信服务的窗口映射
        getCrossWindowService().updateWindowMap();
      } else {
        this.toggleMainWindow();
      }
    });
  }
  
    /**
   * 显示主窗口
   */
  public static showMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed() && this.mainWindow.isVisible()) {
      return;
    }
    log.info('显示主窗口');
    if (process.platform === 'darwin') {
      app.show();
      app.focus({ steal: true });
    }
    this.mainWindow?.show();
    // 给渲染进程发送窗口显示事件
    this.mainWindow?.getWindow()?.webContents.send(WINDOW.SHOW_EVENT);
  }

  /**
   * 隐藏主窗口
   */
  public static hideMainWindow(): void {
    log.info('隐藏主窗口');
    this.mainWindow?.hide();
  }
  
    /**
   * 切换主窗口显示状态
   */
  public static toggleMainWindow(): void {
    // 设置标志位，防止失焦隐藏冲突
    this.isToggling = true;

    // 如果窗口不存在或已销毁，创建新窗口
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      log.debug('窗口不存在或已销毁，创建新窗口');
      this.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);

      // 同步钉住状态与置顶状态
      if (this.pinned && this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.setAlwaysOnTop(true);
      }

      // 更新跨窗口通信服务的窗口映射
      getCrossWindowService().updateWindowMap();

      this.showMainWindow();
      // 在短时间后重置标志位
      setTimeout(() => { this.isToggling = false; }, 100);
      return;
    }

    // 检查窗口可见性
    const isVisible = this.mainWindow.isVisible();
    
    if (isVisible) {
      this.hideMainWindow();
    } else {
      this.showMainWindow();
    }
    // 在短时间后重置标志位
    setTimeout(() => { this.isToggling = false; }, 100);
  }
  
  /**
   * 显示剪贴板历史窗口
   */
  public static async showClipboardHistory(): Promise<void> {
    log.info('显示剪贴板历史窗口');

    // 🔥 获取最新设置，而不是使用缓存的设置
    const currentSettings = settingsService.getSettings();
    console.log('🚀 WindowManager.showClipboardHistory - 当前CONFIG.clipboard:', currentSettings.clipboard); // 调试

    // 如果配置为居中窗口样式，改为在主窗口中显示剪贴板历史
    if (currentSettings.clipboard.clipboardWindowStyle === 'center-window') {
      log.info('使用主窗口显示剪贴板历史');

      // 确保在主窗口模式下，独立窗口实例被清理
      if (this.clipboardHistoryWindow && !this.clipboardHistoryWindow.isDestroyed()) {
        log.debug('主窗口模式：清理旧的独立窗口实例');
        this.clipboardHistoryWindow.destroy();
        this.clipboardHistoryWindow = null;
      }

      await this.showMainWindowClipboardHistory();
      return;
    }

    // 底部栏样式继续使用独立窗口
    if (!this.clipboardHistoryWindow || this.clipboardHistoryWindow.isDestroyed()) {
      console.log('🚀 创建新的剪贴板窗口，样式:', currentSettings.clipboard.clipboardWindowStyle); // 调试
      this.clipboardHistoryWindow = new ClipboardHistoryWindow(currentSettings.clipboard.clipboardWindowStyle);

      // 更新跨窗口通信服务的窗口映射
      getCrossWindowService().updateWindowMap();
    }
    await this.clipboardHistoryWindow.show();
  }

  /**
   * 在主窗口中显示剪贴板历史
   */
  public static async showMainWindowClipboardHistory(): Promise<void> {
    log.info('在主窗口中显示剪贴板历史');

    // 关键：在显示主窗口前先同步记录当前焦点
    try {
      await FocusManager.rememberCurrentFocus();
      log.debug('主窗口模式：焦点记录完成');
    } catch (error) {
      log.error('主窗口模式：焦点记录失败', error);
    }

    // 确保主窗口存在并显示
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      this.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
    }

    this.showMainWindow();

    // 向渲染进程发送事件，通知切换到剪贴板历史模式
    // 为避免项目启动时自动展示剪贴板历史，需确保在应用初始化阶段不会调用该方法
    const browserWindow = this.mainWindow.getWindow();
    if (browserWindow && !browserWindow.isDestroyed()) {
      browserWindow.webContents.send(WINDOW.SHOW_CLIPBOARD_HISTORY);
    }
  }

  /**
   * 隐藏剪贴板历史窗口
   */
  public static hideClipboardHistory(): void {
    log.info('隐藏剪贴板历史窗口');

    // 🔥 获取最新设置
    const currentSettings = settingsService.getSettings();

    // 如果当前是主窗口模式，隐藏主窗口
    if (currentSettings.clipboard.clipboardWindowStyle === 'center-window') {
      // 在主窗口模式下，发送重置事件给主窗口
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        const browserWindow = this.mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send('clipboard-window-reset');
        }
      }
      this.hideMainWindow();
      return;
    }

    // 否则隐藏独立的剪贴板历史窗口
    if (this.clipboardHistoryWindow) {
      this.clipboardHistoryWindow.hide();
    }
  }

  /**
   * 切换剪贴板历史窗口显示状态
   */
  public static async toggleClipboardHistory(): Promise<void> {
    // 🔥 获取最新设置
    const currentSettings = settingsService.getSettings();
    
    // 如果配置为居中窗口样式，使用主窗口模式
    if (currentSettings.clipboard.clipboardWindowStyle === 'center-window') {
      if (!this.mainWindow || this.mainWindow.isDestroyed() || !this.mainWindow.isVisible()) {
        await this.showClipboardHistory();
      } else {
        this.hideClipboardHistory();
      }
      return;
    }
    
    // 底部栏样式使用独立窗口
    if (!this.clipboardHistoryWindow || this.clipboardHistoryWindow.isDestroyed()) {
      await this.showClipboardHistory();
      return;
    }

    await this.clipboardHistoryWindow.toggle();
  }

  /**
   * 更新剪贴板历史窗口样式
   */
  public static async updateClipboardHistoryStyle(style?: string): Promise<void> {
    // 🔥 获取最新设置
    const currentSettings = settingsService.getSettings();
    
    // 如果传入了具体的样式，使用传入的样式；否则使用配置中的样式
    const clipboardWindowStyle = style || currentSettings.clipboard.clipboardWindowStyle;

    // 记录切换前窗口是否可见，只有用户已主动打开时才继续展示
    const wasVisiblePrev = !!(this.clipboardHistoryWindow &&
      !this.clipboardHistoryWindow.isDestroyed() &&
      this.clipboardHistoryWindow.isVisible());

    if (clipboardWindowStyle === 'center-window') {
      // 切换到居中窗口样式：销毁独立窗口实例
      if (this.clipboardHistoryWindow && !this.clipboardHistoryWindow.isDestroyed()) {
        this.clipboardHistoryWindow.destroy();
        this.clipboardHistoryWindow = null;
      }

      // 仅在原本窗口可见（例如用户正在查看历史时）才展示主窗口内的剪贴板历史。
      if (wasVisiblePrev) {
        await this.showMainWindowClipboardHistory();
      }
    } else {
      // 切换到底部栏等独立窗口样式
      // 1. 隐藏主窗口（如果它当前可见）
      if (this.mainWindow && !this.mainWindow.isDestroyed() && this.mainWindow.isVisible()) {
        this.hideMainWindow();
      }

      // 2. 创建或更新独立窗口，但仅在之前已可见时才立即展示，避免启动时自动弹出
      if (this.clipboardHistoryWindow && !this.clipboardHistoryWindow.isDestroyed()) {
        this.clipboardHistoryWindow.updateStyle(clipboardWindowStyle as any);
        if (wasVisiblePrev) {
          this.clipboardHistoryWindow.show();
        }
      } else {
        this.clipboardHistoryWindow = new ClipboardHistoryWindow(clipboardWindowStyle as any);
        if (wasVisiblePrev) {
          this.clipboardHistoryWindow.show();
        }
      }
    }
  }

  /**
   * 获取剪贴板历史窗口实例
   */
  public static getClipboardHistoryWindow(): ClipboardHistoryWindow | null {
    return this.clipboardHistoryWindow;
  }
  
  /**
   * 切换开发者工具
   */
  public static toggleDevTools(): void {
    this.mainWindow?.toggleDevTools();
    // this.petWindow?.toggleDevTools();
    this.floatingBall?.toggleDevTools();
    this.clipboardHistoryWindow?.toggleDevTools();
  }
  
  /**
   * 在macOS下恢复窗口
   */
  public static restoreMainWindow(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      this.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
      
      // 同步钉住状态与置顶状态
      if (this.pinned && this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.setAlwaysOnTop(true);
      }
    } else {
      this.showMainWindow();
    }
  }
  
  /**
   * 获取主窗口实例
   */
  public static getMainWindow(): MainWindow | null {
    return this.mainWindow;
  }
  
  /**
   * 获取悬浮球实例
   */
  public static getFloatingBall(): FloatingBall | null {
    return this.floatingBall;
  }

  /**
   * 获取宠物窗口实例
   */
  public static getPetWindow(): PetWindow | null {
    return this.petWindow;
  }

  /**
   * 获取或创建截图窗口实例
   */
  public static getScreenCaptureWindow(): ScreenCaptureWindow {
    if (!this.screenCaptureWindow) {
      this.screenCaptureWindow = new ScreenCaptureWindow(
        this.floatingBall ? this.floatingBall.getWindow() : null
      );
    }
    return this.screenCaptureWindow;
  }
  
  /**
   * 启动截图模式
   */
  public static startScreenCapture(): void {
    // 检查多屏检测器是否已初始化
    if (!multiScreenDetector.getIsInitialized()) {
      log.warn('多屏检测器未初始化，使用单屏模式');
      const captureWindow = this.getScreenCaptureWindow();
      captureWindow.startCapture();
      return;
    }

    // 检查是否为多显示器环境
    if (multiScreenDetector.isMultiDisplayEnvironment()) {
      // 多显示器环境，直接启动所有屏幕截图
      log.info('检测到多显示器环境，启动所有屏幕截图');
      this.startMultiScreenCapture();
    } else {
      // 单显示器环境，使用传统截图
      log.info('单显示器环境，启动传统截图');
      const captureWindow = this.getScreenCaptureWindow();
      captureWindow.startCapture();
    }
  }

  /**
   * 启动多屏截图模式
   */
  public static startMultiScreenCapture(): void {
    const manager = this.getMultiScreenCaptureManager();
    manager.startMultiScreenCapture();
  }

  /**
   * 启动单显示器截图
   * @param displayId 显示器ID
   */
  public static startSingleDisplayCapture(displayId: string): void {
    const manager = this.getMultiScreenCaptureManager();
    manager.startSingleDisplayCapture(displayId);
  }

  /**
   * 获取或创建多屏截图管理器实例
   */
  public static getMultiScreenCaptureManager(): MultiScreenCaptureManager {
    if (!this.multiScreenCaptureManager) {
      this.multiScreenCaptureManager = new MultiScreenCaptureManager(
        this.floatingBall ? this.floatingBall.getWindow() : null
      );
    }
    return this.multiScreenCaptureManager;
  }
  
  /**
   * 关闭截图窗口
   */
  public static closeScreenCaptureWindow(): void {
    // 关闭多屏截图管理器
    if (this.multiScreenCaptureManager) {
      this.multiScreenCaptureManager.closeAllCaptureWindows();
    }

    // 关闭单屏截图窗口
    if (this.screenCaptureWindow) {
      this.screenCaptureWindow.closeWindow();
      this.screenCaptureWindow = null;
      log.info('截图窗口已关闭');
    }
  }
  
  /**
   * 关闭所有窗口
   */
  public static closeAllWindows(): void {
    log.info('关闭所有窗口...');
    
    // 关闭主窗口
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.destroy();
    }
    
    // 关闭悬浮球
    if (this.floatingBall && !this.floatingBall.isDestroyed()) {
      this.floatingBall.destroy();
    }
    
    // 关闭宠物窗口
    if (this.petWindow && !this.petWindow.isDestroyed()) {
      this.petWindow.destroy();
    }
    
    // 关闭剪贴板历史窗口
    if (this.clipboardHistoryWindow && !this.clipboardHistoryWindow.isDestroyed()) {
      this.clipboardHistoryWindow.destroy();
    }
    
    // 关闭设置窗口
    if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
      this.settingsWindow.close();
    }
    
    // 关闭截图窗口
    if (this.screenCaptureWindow && !this.screenCaptureWindow.isDestroyed()) {
      this.screenCaptureWindow.destroy();
    }
  }

  /**
   * 清理资源
   */
  public static cleanup(): void {
    unregisterAllShortcuts();
    cleanupAppShortcuts();
    this.clipboardHistoryWindow?.destroy();
    this.settingsWindow?.close();

    // 清理跨窗口通信服务
    getCrossWindowService().cleanup();

    this.mainWindow = null;
    this.floatingBall = null;
    this.clipboardHistoryWindow = null;
    this.settingsWindow = null;
    this.screenCaptureWindow = null;
  }

  /**
   * 设置窗口是否可调整大小
   */
  public static setPetResizable(resizable: boolean): boolean {
    if (!this.petWindow) return false;
    this.petWindow.getWindow().setResizable(resizable);
    return true;
  }

  /**
   * 获取窗口是否可调整大小
   */
  public static isPetResizable(): boolean {
    if (!this.petWindow) return false;
    return this.petWindow.getWindow().isResizable();
  }

  /**
   * 设置窗口是否忽略鼠标事件
   */
  public static setPetIgnoreMouseEvents(ignore: boolean): boolean {
    if (!this.petWindow) return false;
    this.petWindow.getWindow().setIgnoreMouseEvents(ignore, { forward: true });
    return true;
  }

  /**
   * 获取全局配置
   */
  public static getConfig(): any {
    return (global as any).config || {};
  }

  /**
   * 设置窗口钉住状态
   */
  public static setPinned(pinned: boolean): void {
    log.info(`正在设置窗口钉住状态为: ${pinned}`);
    this.pinned = pinned;
    
    // 同时设置主窗口的置顶状态
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      log.info(`同步设置主窗口置顶状态为: ${pinned}`);
      this.mainWindow.setAlwaysOnTop(pinned);
    } else {
      log.warn('主窗口不存在或已销毁，无法同步置顶状态');
    }
    
    log.info(`窗口钉住状态已设置为: ${pinned}`);
  }

  /**
   * 获取窗口钉住状态
   */
  public static isPinned(): boolean {
    return this.pinned;
  }

  /**
   * 获取切换状态
   */
  public static getIsToggling(): boolean {
    return this.isToggling;
  }

  /**
   * 显示设置窗口
   */
  public static showSettingsWindow(): void {
    log.info('显示设置窗口');
    if (!this.settingsWindow || this.settingsWindow.isDestroyed()) {
      this.settingsWindow = new SettingsWindow();

      // 更新跨窗口通信服务的窗口映射
      getCrossWindowService().updateWindowMap();
    }
    this.settingsWindow.show();
  }

  /**
   * 隐藏设置窗口
   */
  public static hideSettingsWindow(): void {
    log.info('隐藏设置窗口');
    if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
      this.settingsWindow.hide();
    }
  }

  /**
   * 切换设置窗口显示状态
   */
  public static toggleSettingsWindow(): void {
    if (!this.settingsWindow || this.settingsWindow.isDestroyed()) {
      this.showSettingsWindow();
    } else {
      this.settingsWindow.toggle();
    }
  }

  /**
   * 关闭设置窗口
   */
  public static closeSettingsWindow(): void {
    log.info('关闭设置窗口');
    if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
      this.settingsWindow.close();
      this.settingsWindow = null;
    }
  }

  /**
   * 获取设置窗口实例
   */
  public static getSettingsWindow(): SettingsWindow | null {
    return this.settingsWindow;
  }

  /**
   * 向所有窗口广播主题变化事件
   */
  public static broadcastThemeChange(theme: ThemeType): void {
    try {
      // 向主窗口广播
      const mainWindow = this.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        const browserWindow = mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(THEME.CHANGED, theme);
        }
      }

      // 向设置窗口广播
      const settingsWindow = this.getSettingsWindow();
      if (settingsWindow && !settingsWindow.isDestroyed()) {
        const browserWindow = settingsWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(THEME.CHANGED, theme);
        }
      }

      // 向剪贴板历史窗口广播
      const clipboardHistoryWindow = this.getClipboardHistoryWindow();
      if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
        const browserWindow = clipboardHistoryWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(THEME.CHANGED, theme);
        }
      }

      console.log(`主题变化已广播到所有窗口: ${theme}`);
    } catch (error) {
      console.error('广播主题变化失败:', error);
    }
  }

  /**
   * 向所有窗口广播剪贴板设置变化事件
   */
  public static broadcastClipboardSettingsChange(clipboardSettings: any): void {
    try {
      // 向主窗口广播
      const mainWindow = this.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        const browserWindow = mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(CLIPBOARD_SETTINGS.CHANGED, clipboardSettings);
        }
      }

      // 向设置窗口广播
      const settingsWindow = this.getSettingsWindow();
      if (settingsWindow && !settingsWindow.isDestroyed()) {
        const browserWindow = settingsWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(CLIPBOARD_SETTINGS.CHANGED, clipboardSettings);
        }
      }

      // 向剪贴板历史窗口广播
      const clipboardHistoryWindow = this.getClipboardHistoryWindow();
      if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
        const browserWindow = clipboardHistoryWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(CLIPBOARD_SETTINGS.CHANGED, clipboardSettings);
        }
      }

      console.log(`剪贴板设置变化已广播到所有窗口:`, clipboardSettings);
    } catch (error) {
      console.error('广播剪贴板设置变化失败:', error);
    }
  }

  /**
   * 向所有窗口广播用户状态变化事件
   */
  public static broadcastUserStatusChange(user: any): void {
    try {
      // 向主窗口广播
      const mainWindow = this.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        const browserWindow = mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(USER_STATUS.CHANGED, user);
        }
      }

      // 向设置窗口广播
      const settingsWindow = this.getSettingsWindow();
      if (settingsWindow && !settingsWindow.isDestroyed()) {
        const browserWindow = settingsWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(USER_STATUS.CHANGED, user);
        }
      }

      // 向剪贴板历史窗口广播
      const clipboardHistoryWindow = this.getClipboardHistoryWindow();
      if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
        const browserWindow = clipboardHistoryWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(USER_STATUS.CHANGED, user);
        }
      }

      console.log(`用户状态变化已广播到所有窗口:`, user);
    } catch (error) {
      console.error('广播用户状态变化失败:', error);
    }
  }

  /**
   * 向所有窗口广播语言变化事件
   */
  public static broadcastLanguageChange(language: LanguageType): void {
    try {
      // 向主窗口广播
      const mainWindow = this.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        const browserWindow = mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(LANGUAGE.CHANGED, language);
        }
      }

      // 向设置窗口广播
      const settingsWindow = this.getSettingsWindow();
      if (settingsWindow && !settingsWindow.isDestroyed()) {
        const browserWindow = settingsWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(LANGUAGE.CHANGED, language);
        }
      }

      // 向剪贴板历史窗口广播
      const clipboardHistoryWindow = this.getClipboardHistoryWindow();
      if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
        const browserWindow = clipboardHistoryWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(LANGUAGE.CHANGED, language);
        }
      }

      console.log(`语言变化已广播到所有窗口: ${language}`);
    } catch (error) {
      console.error('广播语言变化失败:', error);
    }
  }

  /**
   * 向所有窗口广播清除token事件
   */
  public static broadcastClearAllTokens(): void {
    try {
      // 向主窗口广播
      const mainWindow = this.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        const browserWindow = mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(AUTH.CLEAR_ALL_TOKENS);
        }
      }

      // 向设置窗口广播
      const settingsWindow = this.getSettingsWindow();
      if (settingsWindow && !settingsWindow.isDestroyed()) {
        const browserWindow = settingsWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(AUTH.CLEAR_ALL_TOKENS);
        }
      }

      // 向剪贴板历史窗口广播
      const clipboardHistoryWindow = this.getClipboardHistoryWindow();
      if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
        const browserWindow = clipboardHistoryWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(AUTH.CLEAR_ALL_TOKENS);
        }
      }

      // 向宠物窗口广播
      const petWindow = this.getPetWindow();
      if (petWindow && !petWindow.isDestroyed()) {
        const browserWindow = petWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(AUTH.CLEAR_ALL_TOKENS);
        }
      }

      // 向悬浮球窗口广播
      const floatingBall = this.getFloatingBall();
      if (floatingBall && !floatingBall.isDestroyed()) {
        const browserWindow = floatingBall.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(AUTH.CLEAR_ALL_TOKENS);
        }
      }

      console.log('✅ 清除token事件已广播到所有窗口');
    } catch (error) {
      console.error('广播清除token事件失败:', error);
    }
  }

  /**
   * 设置主窗口紧凑模式
   */
  public static setMainWindowCompactMode(compact: boolean): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.setCompactMode(compact);
    }
  }

    // 移动窗口
  public static moveWindow(mouseData: { mouseX: number, mouseY: number, width: number, height: number }): void {
    const { x, y } = screen.getCursorScreenPoint();
    const originWindow = WindowManager.getMainWindow();
    if (!originWindow) return;
    originWindow.setBounds({ x: x - mouseData.mouseX, y: y - mouseData.mouseY, width: mouseData.width, height: mouseData.height });
    getWinPosition.setPosition(x - mouseData.mouseX, y - mouseData.mouseY);
  }
}