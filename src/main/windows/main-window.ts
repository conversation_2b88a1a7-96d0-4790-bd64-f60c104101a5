import { BrowserWindow, screen, app } from 'electron';
import path from 'node:path';
import { APP_PATH, isDevelopment, WINDOW_CONFIG } from '../common';
import { WINDOW } from '../../shared/ipc';
import { registerLocalShortcuts } from './shortcuts';
import { IWindowManagerStatic, IMainWindow } from '../types/window';
import { DisplayScreenType } from '../../shared/types/settings';
import { settingsService } from '../services/settingsService';
import * as log from '../logging';

/**
 * 主窗口类
 * 负责主窗口的创建和管理
 */
export class MainWindow implements IMainWindow {
  private window: BrowserWindow | null = null;
  private windowManager: IWindowManagerStatic | null;
  private currentDisplayScreen: DisplayScreenType = 'mouse';
  // 添加位置记忆属性
  private lastPosition: { x: number; y: number } | null = null;
  private lastDisplayScreen: DisplayScreenType | null = null;

  constructor(windowManager?: IWindowManagerStatic) {
    this.windowManager = windowManager || null;
    this.createWindow();
  }

  /**
   * 创建主窗口
   */
  private createWindow(): void {
    this.window = new BrowserWindow({
      width: WINDOW_CONFIG.main.width,
      height: WINDOW_CONFIG.main.height, // 初始使用紧凑高度
      transparent: true,
      frame: false,
      resizable: isDevelopment,
      show: false,
      alwaysOnTop: false,
      skipTaskbar: !isDevelopment,
      webPreferences: {
        preload: path.join(APP_PATH, '.vite/build/preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        devTools: isDevelopment,
        webSecurity: !isDevelopment
      },
    });

    this.loadContent();
    this.setupEventListeners();

    // 如果有窗口管理器，注册本地快捷键
    if (this.windowManager && this.window) {
      registerLocalShortcuts(this.window, this.windowManager);
    }

    // 注意：不在这里设置初始位置，位置设置由 show() 方法处理
    // 这样可以确保位置恢复逻辑的一致性

    log.debug('主窗口创建成功，窗口处于隐藏状态（紧凑模式）');
  }

  /**
   * 加载窗口内容
   */
  private loadContent(): void {
    if (!this.window) return;

    // 记录当前路径，帮助调试
    log.debug('Current directory:', __dirname);

    log.debug('App path:', APP_PATH);

    if (isDevelopment) {
      // 添加重试机制，确保Vite服务器已启动
      const tryLoadURL = (retryCount = 0, maxRetries = 10) => {
        this.window?.loadURL('http://localhost:5173')
          .then(() => {
            log.debug('主窗口成功连接到Vite开发服务器');
            this.window?.webContents.openDevTools({ mode: 'detach' });
          })
          .catch(err => {
            log.error(`主窗口连接Vite服务器失败(${retryCount}): ${err.message}`);
            if (retryCount < maxRetries) {
              log.debug(`主窗口${retryCount + 1}秒后重试连接...`);
              setTimeout(() => tryLoadURL(retryCount + 1), 1000);
            } else {
              log.error('主窗口达到最大重试次数，无法连接Vite服务器');
            }
          });
      };
      
      tryLoadURL();
    } else {
      // 根据应用路径构建HTML路径
      const htmlPath = path.join(APP_PATH, '.vite/renderer/main_window/index.html');
      log.debug('HTML path:', htmlPath);

      // 加载HTML
      this.window.loadFile(htmlPath).catch(err => {
        log.error('Failed to load HTML:', err);
      });
    }
  }

  /**
   * 设置窗口事件监听器
   */
  private setupEventListeners(): void {
    if (!this.window) return;

    // 窗口失去焦点时自动隐藏
    this.window.on('blur', this.handleWindowBlur.bind(this));

    // 窗口隐藏事件
    this.window.on('hide', () => {
      log.debug('主窗口已隐藏');
      // 保存当前位置和屏幕设置
      this.saveCurrentPosition();
      this.window?.webContents.send(WINDOW.HIDE_EVENT);
    });
    
    // 窗口获得焦点事件
    this.window.on('focus', () => {
      log.debug('主窗口获得焦点');
    });

    // 开启错误捕获
    this.window.webContents.on('render-process-gone', (_, details) => {
      log.error('主窗口渲染进程崩溃:', details.reason);
    });

    this.window.webContents.on('did-fail-load', () => {
      log.error('主窗口页面加载失败');
    });
  }

  /**
   * 处理窗口失焦事件
   */
  private handleWindowBlur(): void {
    if (!this.window) {
      // if (!this.window || this.window.webContents.isDevToolsOpened()) {
      return;
    }

    // 检查窗口是否正在切换，如果是则不自动隐藏
    if (this.windowManager && this.windowManager.getIsToggling()) {
      return;
    }

    // 检查窗口是否被钉住，如果被钉住则不自动隐藏
    if (this.windowManager && (this.windowManager as any).isPinned?.()) {
      return;
    }
    
    // 如果窗口可见且未被销毁，则隐藏
    if (this.window && !this.window.isDestroyed() && this.window.isVisible()) {
      this.window.hide();
    }
  }

  /**
   * 显示窗口
   */
  public show(): void {
    if (!this.window || this.window.isDestroyed()) {
      this.createWindow();
      // 创建后恢复位置再显示
      if (this.window) {
        // 恢复上次保存的位置，如果没有保存的位置则使用默认逻辑
        if (this.lastPosition && this.lastDisplayScreen) {
          this.restoreLastPosition();
        } else {
          // 如果没有保存的位置，根据设置初始化位置
          const currentSettings = settingsService.getSettings();
          this.updateScreenPosition(currentSettings.general.displayScreen);
        }
        this.window.show();
        this.window.focus();
      }
      return;
    }

    // 恢复上次保存的位置，如果没有保存的位置则使用默认逻辑
    if (this.lastPosition && this.lastDisplayScreen) {
      this.restoreLastPosition();
    } else {
      // 如果没有保存的位置，根据设置初始化位置
      const currentSettings = settingsService.getSettings();
      this.updateScreenPosition(currentSettings.general.displayScreen);
    }

    // 确保窗口可见并获得焦点
    if (!this.window.isVisible()) {
      this.window.show();
    }

    this.window.focus();
  }

  /**
   * 隐藏窗口
   */
  public hide(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide();
    } else {
      log.info('主窗口不存在或已销毁，无法隐藏');
    }
  }

  /**
   * 调整窗口大小
   */
  public resize(width: number, height: number): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.setSize(width, height);
    }
  }

  /**
   * 切换窗口开发者工具
   */
  public toggleDevTools(): void {
    if (!isDevelopment || !this.window || this.window.isDestroyed()) return;

    const devToolsOpened = this.window.webContents.isDevToolsOpened();
    if (devToolsOpened) {
      this.window.webContents.closeDevTools();
    } else {
      // 确保窗口可见
      if (!this.window.isVisible()) {
        this.show();
      }
      this.window.webContents.openDevTools({ mode: 'detach' });
    }
  }

  /**
   * 检查窗口是否可见
   */
  public isVisible(): boolean {
    return this.window ? this.window.isVisible() : false;
  }

  /**
   * 检查窗口是否被销毁
   */
  public isDestroyed(): boolean {
    return this.window ? this.window.isDestroyed() : true;
  }

  /**
   * 获取窗口实例
   */
  public getWindow(): BrowserWindow | null {
    return this.window;
  }

  /**
   * 设置窗口位置
   */
  public setBounds(bounds: { x: number, y: number, width: number, height: number }): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.setBounds(bounds);
    }
  }

  /**
   * 设置窗口是否始终置顶
   */
  public setAlwaysOnTop(alwaysOnTop: boolean): void {
    if (this.window && !this.window.isDestroyed()) {
      try {
        this.window.setAlwaysOnTop(alwaysOnTop, 'main-menu');
        log.info(`主窗口置顶状态已设置为: ${alwaysOnTop}`);
      } catch (error) {
        log.error('设置主窗口置顶状态失败:', error);
      }
    } else {
      log.warn('主窗口不存在或已销毁，无法设置置顶状态');
    }
  }

  /**
   * 保存当前位置和屏幕设置
   */
  private saveCurrentPosition(): void {
    if (!this.window || this.window.isDestroyed()) {
      return;
    }

    const bounds = this.window.getBounds();
    this.lastPosition = { x: bounds.x, y: bounds.y };
    this.lastDisplayScreen = this.currentDisplayScreen;
    
    log.debug(`保存窗口位置: (${bounds.x}, ${bounds.y}), 屏幕: ${this.currentDisplayScreen}`);
  }

  /**
   * 恢复上次保存的位置
   */
  private restoreLastPosition(): void {
    if (!this.window || this.window.isDestroyed() || !this.lastPosition) {
      return;
    }

    // 首先尝试直接恢复到保存的位置
    const x = this.lastPosition.x;
    const y = this.lastPosition.y;

    // 检查保存的位置是否在任何可用屏幕范围内
    const allDisplays = screen.getAllDisplays();
    let isPositionValid = false;
    // let targetDisplay = null; // 临时注释掉，因为当前跳过验证逻辑

    for (const display of allDisplays) {
      const { x: screenX, y: screenY } = display.workArea;
      const { width: screenWidth, height: screenHeight } = display.workAreaSize;

      // 使用更宽松的验证逻辑：只要窗口的左上角在屏幕的扩展范围内就认为有效
      // 允许窗口稍微超出屏幕边界，只要大部分内容仍然可见
      const margin = 100; // 允许100像素的边界容差
      const minVisibleWidth = 200; // 至少200像素宽度可见
      const minVisibleHeight = 50; // 至少50像素高度可见

      // 检查窗口是否在扩展的屏幕范围内
      const inExtendedBounds = x >= screenX - margin &&
                              x <= screenX + screenWidth + margin &&
                              y >= screenY - margin &&
                              y <= screenY + screenHeight + margin;

      // 检查是否有足够的可见区域
      const visibleWidth = Math.min(x + WINDOW_CONFIG.main.width, screenX + screenWidth) - Math.max(x, screenX);
      const visibleHeight = Math.min(y + WINDOW_CONFIG.main.height, screenY + screenHeight) - Math.max(y, screenY);
      const hasEnoughVisibleArea = visibleWidth >= minVisibleWidth && visibleHeight >= minVisibleHeight;

      if (inExtendedBounds && hasEnoughVisibleArea) {
        isPositionValid = true;
        // targetDisplay = display; // 临时注释掉
        log.debug(`位置在屏幕 ${display.id} 内有效`);
        break;
      }
    }

    // 临时禁用位置验证，直接恢复保存的位置进行测试
    // TODO: 如果这样工作正常，说明位置验证逻辑有问题
    log.debug(`临时跳过位置验证，直接恢复保存的位置 (${x}, ${y})`);
    isPositionValid = true;

    this.window.setPosition(x, y);
    this.currentDisplayScreen = this.lastDisplayScreen || 'mouse';
  }

  /**
   * 根据设置更新窗口显示屏幕位置
   */
  public updateScreenPosition(displayScreen: DisplayScreenType): void {
    if (!this.window || this.window.isDestroyed()) {
      return;
    }

    // 保存当前设置
    this.currentDisplayScreen = displayScreen;

    let targetDisplay;

    if (displayScreen === 'main') {
      // 使用主屏幕
      targetDisplay = screen.getPrimaryDisplay();
    } else {
      // 使用鼠标所在屏幕
      const mousePoint = screen.getCursorScreenPoint();
      targetDisplay = screen.getDisplayNearestPoint(mousePoint);
    }

    const { width, height } = targetDisplay.workAreaSize;
    const { x: screenX, y: screenY } = targetDisplay.workArea;
    
    // 计算窗口在目标屏幕中央的位置
    const x = screenX + Math.floor(width / 2 - WINDOW_CONFIG.main.width / 2);
    const y = screenY + Math.floor(height / 2 - WINDOW_CONFIG.main.height / 2);
    
    this.window.setPosition(x, y);
    
    log.debug(`主窗口位置已更新到${displayScreen === 'main' ? '主屏幕' : '鼠标所在屏幕'}: (${x}, ${y})`);
  }

  /**
   * 销毁窗口
   */
  public destroy(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy();
      this.window = null;
    }
  }

  /**
   * 设置窗口是否使用紧凑模式
   * @param compact 是否使用紧凑模式（只显示搜索栏和状态栏）
   */
  public setCompactMode(compact: boolean): void {
    if (!this.window || this.window.isDestroyed()) {
      return;
    }

    const currentBounds = this.window.getBounds();
    const targetHeight = compact ? WINDOW_CONFIG.main.compactHeight : WINDOW_CONFIG.main.height;
    
    // 如果高度已经是目标高度，则不需要调整
    if (Math.abs(currentBounds.height - targetHeight) < 5) {
      return;
    }

    // 保持顶部y坐标不变，仅向下延伸或收缩
    this.window.setBounds({
      x: currentBounds.x,
      y: currentBounds.y,
      width: currentBounds.width,
      height: targetHeight
    });

    log.debug(`主窗口${compact ? '进入' : '退出'}紧凑模式，高度: ${currentBounds.height} -> ${targetHeight}`);
  }
} 