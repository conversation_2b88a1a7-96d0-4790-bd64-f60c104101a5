import { registerAppHandlers } from './handlers/app-handler';
import { registerFileHandlers } from './handlers/file-handler';
import { registerWindowHandlers } from './handlers/window-handler';
import { registerDBHandlers } from './handlers/db-handler';
import { registerMcpHandlers } from './handlers/mcp-handler';
import { registerStoreHandlers } from './handlers/store-handler';
import { registerClipboardHandlers } from './handlers/clipboard-handler';
import { registerClipboardEmitters } from './emitters/clipboard-emitter';
import { registerPetHandlers } from './handlers/pet-handler';
import { registerProxyHandlers } from './handlers/proxy-handler';
import { registerShortcutHandlers } from './handlers/shortcut-handler';
import { registerSystemHandlers } from './handlers/system-handler';
import { registerAuthHandlers } from './handlers/auth-handler';
import { registerSettingsHandlers } from './handlers/settings-handler';
import { registerCrossWindowHandlers } from './handlers/cross-window-handler';
import { registerScreenCaptureHandlers } from './handlers/screen-capture-handler';

/**
 * 注册所有IPC处理程序和事件发射器
 */
export function registerAllIpcHandlers(): void {
  try {
    // 注册各种处理程序
    const handlers = [
      registerAppHandlers,
      registerFileHandlers,
      registerWindowHandlers,
      registerDBHandlers,
      registerMcpHandlers,
      registerStoreHandlers,
      registerAuthHandlers,
      registerClipboardHandlers,
      registerPetHandlers,
      registerShortcutHandlers,
      registerProxyHandlers,
      registerSystemHandlers,
      registerSettingsHandlers,
      registerCrossWindowHandlers,
      registerScreenCaptureHandlers,
    ];
    
    // 注册发射器
    const emitters = [
      registerClipboardEmitters,
    ];
    
    // 执行所有处理程序注册
    handlers.forEach(handler => handler());
    
    // 执行所有发射器注册
    emitters.forEach(emitter => emitter());

    console.log('所有IPC处理程序和事件发射器注册成功');
  } catch (error) {
    console.error('注册IPC模块失败:', error);
  }
} 