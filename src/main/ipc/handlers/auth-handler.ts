import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import { AUTH, USER_STATUS } from '../../../shared/ipc';
import { mainCrossWindow } from '../../services/cross-window-communication';
import { WindowManager } from '../../windows/window-manager';
import { getMainProcessToken, updateFieldCache, clearFieldCache } from '../../services/rendererFieldService';

export function registerAuthHandlers(): void {
  // 监听渲染进程的登录成功通知
  ipcMain.on(AUTH.LOGIN_SUCCESS, async () => {
    console.log('✅ 主进程收到登录成功通知，准备处理任务队列...');
    
    // 先更新主进程的token缓存，确保后续的getMainProcessToken能获取到最新token
    try {
      const { updateFieldCache } = await import('../../services/rendererFieldService');
      // 从渲染进程获取最新的token并更新缓存
      const mainWindow = BrowserWindow.getAllWindows().find((win: BrowserWindow) => !win.isDestroyed());
      if (mainWindow) {
        const latestToken = await mainWindow.webContents.executeJavaScript(`
          (() => {
            try {
              const authData = localStorage.getItem('auth-storage');
              if (authData) {
                const parsed = JSON.parse(authData);
                const data = parsed.state || parsed;
                return data?.token || null;
              }
              return null;
            } catch (error) {
              console.error('获取token失败:', error);
              return null;
            }
          })()
        `);
        
        if (latestToken) {
          updateFieldCache('token', latestToken);
          console.log('✅ 已更新主进程token缓存:', latestToken.substring(0, 20) + '...');
        } else {
          console.warn('⚠️ 无法从渲染进程获取最新token');
        }
      }
    } catch (error) {
      console.error('更新主进程token缓存失败:', error);
    }
    
    // 获取最新的用户状态（从渲染进程广播过来的状态）
    // 这里我们需要等待渲染进程先更新状态，然后广播给所有窗口
    // 由于渲染进程已经通过broadcastUserStatusChange发送了状态，
    // 我们只需要触发auth:success事件供主进程内部使用
    mainCrossWindow.emitUnified('auth:success', {
      timestamp: Date.now(),
      source: 'renderer-login'
    },  {
      inMain: true,      // 主进程内部处理（供任务队列等服务监听）
      toWindows: true    // 同时通知所有窗口
    });
    
    // 注意：用户状态变化的广播已经在authStore的login方法中通过broadcastUserStatusChange处理了
    // 这里不需要重复发送user-status-changed事件
  });

  // 监听渲染进程的登出通知
  ipcMain.on('auth-logout', async () => {
    console.log('✅ 主进程收到登出通知，准备断开MCP服务连接...');
    
    // 清除主进程的token缓存
    try {
      const { clearFieldCache } = await import('../../services/rendererFieldService');
      clearFieldCache('token');
      console.log('✅ 已清除主进程token缓存');
    } catch (error) {
      console.error('清除主进程token缓存失败:', error);
    }
    
    // 向所有窗口广播清除token事件（只在用户主动登出时）
    try {
      const { WindowManager } = await import('../../windows/window-manager');
      WindowManager.broadcastClearAllTokens();
      console.log('✅ 已向所有窗口广播清除token事件');
    } catch (error) {
      console.error('广播清除token事件失败:', error);
    }
    
    // 使用新的统一事件系统，触发登出事件
    mainCrossWindow.emitUnified('auth:logout', {
      timestamp: Date.now(),
      source: 'renderer-logout'
    }, {
      inMain: true,      // 主进程内部处理（供MCP服务等监听）
      toWindows: true    // 同时通知所有窗口
    });
  });

  // 获取 token
  ipcMain.handle(AUTH.GET_TOKEN, async () => {
    try {
      const token = await getMainProcessToken();
      return token;
    } catch (error) {
      console.error('获取token失败:', error);
      return null;
    }
  });

  // 设置 token（暂时不实现，因为主要的设置逻辑在渲染线程）
  ipcMain.handle(AUTH.SET_TOKEN, async (_, token: string) => {
    try {
      // 更新缓存
      updateFieldCache('token', token);
      return;
    } catch (error) {
      console.error('设置token失败:', error);
      return;
    }
  });

  // 清除 token
  ipcMain.handle(AUTH.CLEAR_TOKEN, async () => {
    try {
      clearFieldCache('token');
      return;
    } catch (error) {
      console.error('清除token失败:', error);
      return;
    }
  });

  // 广播用户状态变化
  ipcMain.handle(AUTH.BROADCAST_USER_STATUS_CHANGE, async (_, user: any) => {
    try {
      console.log('主进程收到用户状态变化广播请求:', user);
      WindowManager.broadcastUserStatusChange(user);
      return true;
    } catch (error) {
      console.error('广播用户状态变化失败:', error);
      return false;
    }
  });
}