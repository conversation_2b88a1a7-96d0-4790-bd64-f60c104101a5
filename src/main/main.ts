import { app, BrowserWindow, protocol, dialog } from 'electron';
import path from 'node:path';
import * as log from './logging';
import { WindowManager } from './windows/window-manager';
import { registerAllIpcHandlers } from './ipc';
import { registerLocalProtocol } from './utils/localProtocol';
import { settingsService } from './services/settingsService';
import { TrayManager } from './tray/tray-manager';
import { extractBunToBindir } from './utils/extract-tools';
import { isMac, isDevelopment } from './common';
import { clipboardService } from './services/clipboardService';
import { mcpService } from './services/mcpService';
import { handleAidoProtocol } from './services/aidoProtocolService';
import { configureProxy } from './utils/proxy';
import { systemService } from './services/systemService';
import { getMainProcessToken } from './services/rendererFieldService';
import { mainCrossWindow } from './services/cross-window-communication';
import { multiScreenDetector } from './utils/multi-screen-detector';
import { ProxyMCPClient } from './services/mcpProxyClient';


// 定义全局函数以供热重载使用
// 添加强制清除缓存的功能
function clearRequireCache(): void {
  // 清除主进程文件的缓存
  Object.keys(require.cache).forEach(key => {
    // 只清除主进程相关的模块缓存
    if (key.includes('src/main') || key.includes('.vite/build')) {
      delete require.cache[key];
      console.log(`清除缓存: ${key}`);
    }
  });
}

// 热重载配置
if (process.env.ELECTRON_RELOAD === '1') {
  try {
    const electronReload = require('electron-reload');
    const watchPaths = [
      path.join(__dirname, '..'), // 监听构建输出目录
      path.join(__dirname, '../../src/main'), // 监听源码目录
    ];
    
    // 监听文件变化的回调
    const onFileChange = (changedFile: string): void => {
      console.log(`检测到文件变化: ${changedFile}`);
      clearRequireCache();
    };
    
    // 更改重载方式以保持调试会话
    electronReload(watchPaths, {
      electron: path.join(__dirname, '../../node_modules', '.bin', 'electron'),
      hardResetMethod: 'quit', // 使用quit代替exit，保持调试会话
      forceHardReset: false, // 不强制硬重置
      // 添加延迟，确保Vite服务器有时间启动
      delay: 1000
    }, onFileChange);
    
    console.log('Electron热重载已启用，监听路径:', watchPaths);
  } catch (err) {
    console.error('配置Electron热重载失败:', err);
  }
}

// 添加全局变量来确保同一个进程中只有一个应用实例
// 这是为了解决Vite热重载导致的重复应用实例问题
declare global {
  // 使用模块扩展代替命名空间
  interface Global {
    isAppInitialized: boolean;
  }
}

// 初始化全局标志
const g = global as any;
g.isAppInitialized = g.isAppInitialized || false;

// 添加全局标志防止重复初始化
let mcpInitializationInProgress = false;
let mcpInitialized = false;

/**
 * 检查用户登录状态和设备ID，如果满足条件则初始化MCP服务
 */
async function checkAndInitMCPService(): Promise<void> {
  // 防止重复初始化
  if (mcpInitializationInProgress) {
    log.info('MCP服务已在初始化中，跳过重复初始化');
    return;
  }

  // 检查MCP服务是否已经加载，如果已加载则跳过
  if (mcpService.isServiceLoaded()) {
    log.info('MCP服务已加载，跳过重复初始化');
    return;
  }

  try {
    mcpInitializationInProgress = true;
    log.info('检查用户登录状态和设备ID以初始化MCP服务...');

    // 1. 首先获取设备ID
    const deviceId = await systemService.getComputerId();
    if (!deviceId) {
      log.warn('无法获取设备ID，跳过MCP服务初始化');
      return;
    }
    log.info(`设备ID已获取: ${deviceId.substring(0, 8)}...`);

    // 2. 检查用户登录状态
    const token = await getMainProcessToken();
    if (!token) {
      log.info('用户未登录，MCP服务将在用户登录后初始化');
      return;
    }
    log.info('用户已登录，开始初始化MCP服务');

    // 3. 用户已登录且有设备ID，可以初始化MCP服务
    await mcpService.load();
    mcpInitialized = true;
    log.info('✅ MCP服务初始化完成');
  } catch (error: any) {
    log.error('检查用户状态并初始化MCP服务失败:', error);
    // 不抛出错误，让应用继续运行
  } finally {
    mcpInitializationInProgress = false;
  }
}

/**
 * 应用初始化
 */
async function initializeApp() {
  // 如果应用已经初始化，跳过初始化过程
  if (g.isAppInitialized) {
    return;
  }

  try {
    // 配置网络代理 - 使用系统代理设置
    await configureProxy();
    
    // 解压Bun，但不等待完成
    extractBunToBindir().catch((error: any) => {
      log.error('[App] 提取Bun失败:', error);
    });
    
    // 注册为 aido:// 协议的默认处理程序
    if (!app.isDefaultProtocolClient('aido')) {
      // 开发模式下需要指定可执行文件路径
      if (process.env.NODE_ENV === 'development' || process.defaultApp) {
        // 开发模式：注册 electron 可执行文件和脚本路径
        const registered = app.setAsDefaultProtocolClient('aido', process.execPath, [
          path.resolve(process.argv[1])
        ]);
        if (registered) {
          log.info('✅ 已注册为 aido:// 协议的默认处理程序 (开发模式)');
        } else {
          log.warn('❌ 注册 aido:// 协议处理程序失败 (开发模式)');
        }
      } else {
        // 生产模式：正常注册
        const registered = app.setAsDefaultProtocolClient('aido');
        if (registered) {
          log.info('✅ 已注册为 aido:// 协议的默认处理程序');
        } else {
          log.warn('❌ 注册 aido:// 协议处理程序失败');
        }
      }
    } else {
      log.info('✓ aido:// 协议处理程序已存在');
    }
    
    // 首先加载配置
    settingsService.loadSettings();
    // 初始化多屏检测器
    multiScreenDetector.initialize();
    // log.info('多屏检测器已初始化');

    // 初始化窗口管理器
    WindowManager.init();
    
    // 注册所有IPC处理程序
    registerAllIpcHandlers();
    // 注册本地协议
    registerLocalProtocol();
    // 初始化托盘
    TrayManager.init();
    
    // 确保剪贴板服务初始化
    clipboardService.init();
    log.info('剪贴板服务已初始化');
    
    // 初始化 MCP 服务 - 改为延迟初始化，等待用户认证完成
    // mcpService.load().catch(error => {
    //   log.error('[App] MCP 服务初始化失败:', error);
    // });
    // log.info('MCP 服务初始化已启动');
    
    // 检查用户登录状态，如果已登录则立即初始化MCP服务
    checkAndInitMCPService().catch((error: any) => {
      log.error('[App] 检查用户状态并初始化MCP服务失败:', error);
    });
    
    // 监听认证成功事件，当用户登录成功时初始化或重连MCP服务
    mainCrossWindow.onUnified('auth:success', async (data, source) => {
      log.info(`收到认证成功事件，尝试处理MCP服务连接... (来源: ${source})`);
      // 检查MCP服务是否已经初始化
      if (mcpService.isServiceLoaded()) {
        log.info('MCP服务已初始化，尝试重连WebSocket...');
        try {
          await mcpService.reconnectWebSocket();
          log.info('✅ MCP WebSocket重连完成');
        } catch (error: any) {
          log.error('MCP WebSocket重连失败:', error);
        }
      } else {
        log.info('MCP服务未初始化，尝试初始化MCP服务...');
        try {
          await checkAndInitMCPService();
          log.info('✅ MCP服务初始化完成');
        } catch (error: any) {
          log.error('MCP服务初始化失败:', error);
        }
      }
    }, {
      fromMain: true,     // 监听主进程内部的认证事件
      fromWindows: true   // 也监听来自窗口的认证事件
    });
    
    // 监听用户登出事件，当用户登出时断开MCP服务连接
    mainCrossWindow.onUnified('auth:logout', async (data, source) => {
      log.info(`收到用户登出事件，断开MCP服务连接... (来源: ${source})`);
      try {
        await mcpService.disconnect();
        log.info('✅ 用户登出时MCP服务连接已断开');
      } catch (error: any) {
        log.error('用户登出时断开MCP服务连接失败:', error);
      }
    }, {
      fromMain: true,     // 监听主进程内部的登出事件
      fromWindows: true   // 也监听来自窗口的登出事件
    });
    
    // 标记应用已经初始化
    g.isAppInitialized = true;
    
  } catch (error) {
    log.error('应用启动失败:', error);
    // 考虑在严重错误时退出应用
    // app.quit(); 
  }
}

// 确保应用只有一个实例运行(进程级别的单例检查)
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  log.info('另一个实例已在运行，本实例即将退出。');
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // 当尝试启动第二个实例时，显示并聚焦现有的主窗口
    log.info('检测到尝试启动第二个实例，激活主窗口。');
    WindowManager.restoreMainWindow(); 
  });
  
  // 设置命令行参数，解决窗口背景和圆角问题
  if (isMac) {
    app.commandLine.appendSwitch('disable-features', 'OverlayScrollbar');
    app.commandLine.appendSwitch('enable-transparent-visuals');
    // 添加 macOS 安全编码支持
    app.commandLine.appendSwitch('enable-secure-restoring-state');
  }

  log.init();
  log.debug('主进程启动（单例模式已获取锁）');

  // 应用准备好后初始化
  app.whenReady().then(() => {
    initializeApp();
    
    // 检查启动时的命令行参数（主要用于Windows）
    const aidoUrl = process.argv.find(arg => arg.startsWith('aido://'));
    if (aidoUrl) {
      log.info(`启动时检测到协议调用: ${aidoUrl}`);
      // 延迟处理，确保应用完全初始化
      setTimeout(() => {
        handleAidoProtocol(aidoUrl).catch(error => {
          log.error('处理启动时协议调用失败:', error);
        });
      }, 2000);
    }
  });

  // 关闭所有窗口时退出应用，macOS除外
  app.on('window-all-closed', () => {
    if (!isMac) {
      app.quit();
    }
  });

  // 设置新窗口创建拦截器，用于处理aido://协议链接
  app.on('web-contents-created', (event, contents) => {
    contents.setWindowOpenHandler(({ url }) => {
      if (url.startsWith('aido://')) {
        // 显示确认对话框
        const choice = dialog.showMessageBoxSync({
          type: 'question',
          buttons: ['确定','取消'],
          defaultId: 0,
          title: '协议确认',
          message: '确定安装此技能？',
          detail: url,
          cancelId: 1
        });
        
        if (choice === 0) {
          // 用户确认，处理协议
          handleAidoProtocol(url).catch((error:Error) => {
            log.error('处理协议链接失败:', error);
            dialog.showErrorBox('错误', `处理链接失败: ${error.message}`);
          });
        }
        
        // 阻止创建新窗口
        return { action: 'deny' };
      }
      
      // 对于其他链接，允许在默认浏览器中打开
      return { action: 'allow' };
    });
  });

  // macOS激活应用时的处理
  app.on('activate', () => {
    WindowManager.restoreMainWindow();
  });

  // macOS 协议处理 - 当应用已运行时收到协议URL
  // 在 initializeApp 函数中添加
  app.on("will-finish-launching", () => {
    app.on("open-url", (event, url) => {
      event.preventDefault();
      log.info(`macOS 协议调用 (will-finish-launching): ${url}`);
      
      if (url.startsWith('aido://')) {
        handleAidoProtocol(url).catch(error => {
          log.error('处理 macOS 协议调用失败:', error);
        });
      }
    });
  });
  
  // 应用退出前清理资源
  app.on('will-quit', (event) => {
    log.info('应用即将退出，开始清理资源...');
    
    try {
      // 清理窗口和托盘
      WindowManager.cleanup();
      TrayManager.cleanup();
      ProxyMCPClient.globalCleanup();
      
      log.info('应用资源清理完成。');
    } catch (error) {
      log.error('清理资源时发生错误:', error);
    }
  });
}