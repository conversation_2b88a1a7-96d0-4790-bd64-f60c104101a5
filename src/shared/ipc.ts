/**
 * IPC 通信模块
 * 集中管理所有IPC通道名称和类型定义
 */

// ================= IPC 常量定义 =================

// 窗口相关
export const WINDOW = {
  HIDE: 'hide-window',
  SHOW: 'show-window',
  TOGGLE: 'toggle-window',
  MOVE_WINDOW: 'move-window',
  SHOW_EVENT: 'window-show',
  HIDE_EVENT: 'window-hide',
  DEV_TOOLS: 'open-devtools',
  SET_RESIZABLE: 'set-resizable',
  IS_RESIZABLE: 'is-resizable',
  SET_IGNORE_MOUSE: 'set-ignore-mouse',
  GET_CONFIG: 'get-config',
  SET_PIN: 'set-pin',
  IS_PINNED: 'is-pinned',
  SHOW_CLIPBOARD_HISTORY: 'show-clipboard-history',
  SHOW_SETTINGS: 'show-settings-window',
  HIDE_SETTINGS: 'hide-settings-window',
  TOGGLE_SETTINGS: 'toggle-settings-window',
  SET_COMPACT_MODE: 'set-compact-mode',
  // 内置功能快捷键触发事件
  SWITCH_TO_TRANSLATOR: 'switch-to-translator',
  SWITCH_TO_AI_CHAT: 'switch-to-ai-chat',
  SWITCH_TO_FILE_SEARCH: 'switch-to-file-search',
  TRIGGER_SCREENSHOT: 'trigger-screenshot',
} as const;

// 桌宠相关
export const PET = {
  GET_MODELS: 'get-models',
  SHOW_TIP: 'show-tip',
} as const;

// 浮动球相关
export const FLOATING_BALL = {
  DRAG: 'floating-ball-drag',
  DRAG_START: 'floating-ball-drag-start',
  DRAG_MOVE: 'floating-ball-drag-move',
  DRAG_END: 'floating-ball-drag-end',
  DRAGGING: 'floating-ball-dragging',
  SNAP_SIDE_CHANGE: 'floating-ball-snap-side-change',
  CLICK: 'floating-ball-click',
  DOUBLE_CLICK: 'floating-ball-double-click',
  LONG_PRESS: 'floating-ball-long-press',
  SHOW: 'floating-ball-show',
  HIDE: 'floating-ball-hide',
  TOGGLE: 'floating-ball-toggle',
  SET_IGNORE_MOUSE: 'floating-ball-set-ignore-mouse',
} as const;


// 截图相关
export const SCREEN_CAPTURE = {
  START: 'screen-capture-start',
  END: 'screen-capture-end',
  CHANGE: 'screen-capture-change',
  CANCEL: 'screen-capture-cancel',
  IMAGE: 'screen-capture-image',
  // 多屏截图相关
  GET_DISPLAYS: 'screen-capture-get-displays',
  START_MULTI: 'screen-capture-start-multi',
  SELECT_DISPLAY: 'screen-capture-select-display',
  MULTI_IMAGE: 'screen-capture-multi-image',
} as const;

// 语音输入相关
export const VOICE_INPUT = {
  START: 'voice-input-start',
  END: 'voice-input-end',
  CHANGE: 'voice-input-change',
} as const;

// 应用相关
export const APP = {
  LAUNCH: 'launch-app',
  GET_APP_LIST: 'get-app-list',
  ICON_UPDATED: 'app-icon-updated', // 新增：应用图标更新事件
} as const;

// 文件相关
export const FILE = {
  GET_RECENT: 'get-recent-files',
  SEARCH: 'search-files',
  OPEN: 'open-file',
  OPEN_LOCATION: 'open-file-location',
  READ_PREVIEW: 'read-file-preview',
  CHECK_EXISTS: 'check-file-exists',
  SELECT_FOLDER: 'select-folder',
} as const;

// 数据库相关
export const DB = {
  ALL: 'db-all',
  GET: 'db-get',
  RUN: 'db-run',
  TRANSACTION: 'db-transaction',
} as const;

// 快捷键相关
export const SHORTCUT = {
  CHECK_AVAILABLE: 'shortcut-check-available',
  REGISTER_APP: 'shortcut-register-app',
  UNREGISTER_APP: 'shortcut-unregister-app',
  GET_APP_SHORTCUTS: 'shortcut-get-app-shortcuts',
  TRIGGER_APP: 'shortcut-trigger-app',
  DISABLE_ALL: 'shortcut-disable-all',
  RESTORE_ALL: 'shortcut-restore-all',
  // 内置功能快捷键
  REGISTER_BUILTIN: 'shortcut-register-builtin',
  UNREGISTER_BUILTIN: 'shortcut-unregister-builtin',
  GET_BUILTIN_SHORTCUTS: 'shortcut-get-builtin-shortcuts',
} as const;

// MCP 相关
export const MCP = {
  LIST_TOOLS: 'mcp-list-tools',
  CALL_TOOL: 'mcp-call-tool',
  GET_CONFIG: 'mcp-get-config',
  PUT_CONFIG: 'mcp-put-config',
  ACTIVATE: 'mcp-activate',
  DEACTIVATED: 'mcp-deactivated',
  ADD_SERVER: 'mcp-add-server',
  UPDATE_SERVER: 'mcp-update-server',
  DELETE_SERVER: 'mcp-delete-server',
  GET_ACTIVE_SERVERS: 'mcp-get-active-servers',
  GET_ALL_TOOLS: 'mcp-get-all-tools',
  GET_MCP_TOOL_BY_ID: 'mcp-get-tool-by-id', // 添加根据ID获取MCP工具的方法
  TOOLS_UPDATED: 'mcp-tools-updated', // 技能列表更新通知
  TOOL_DOWNLOAD_REQUEST: 'mcp-tool-download-request', // MCP技能下载请求
} as const;

// Crypto 相关
export const CRYPTO = {
  HMAC_SHA256_HEX: 'crypto-hmac-sha256-hex',
} as const;

// 系统信息相关
export const SYSTEM = {
  GET_INFO: 'system-get-info',
  GET_COMPUTER_ID: 'system-get-computer-id',
  GET_HOME_PATH: 'system-get-home-path',
} as const;

// Store 相关 (electron-store like API)
export const STORE = {
  GET: 'get-store',
  SET: 'set-store',
} as const;

// 认证相关
export const AUTH = {
  GET_TOKEN: 'auth-get-token',
  SET_TOKEN: 'auth-set-token',
  CLEAR_TOKEN: 'auth-clear-token',
  CLEAR_ALL_TOKENS: 'auth-clear-all-tokens',
  OPEN_LOGIN_DIALOG: 'auth-open-login-dialog',
  LOGIN_SUCCESS: 'auth-login-success',
  LOGOUT: 'auth-logout',
  BROADCAST_USER_STATUS_CHANGE: 'auth-broadcast-user-status-change',
} as const;

// 剪贴板相关
export const CLIPBOARD = {
  GET_HISTORY: 'clipboard-getHistory',
  CLEAR_HISTORY: 'clipboard-clearHistory',
  ON_CHANGE: 'clipboard-onChange',
  WRITE: 'clipboard-write',
  GET_CURRENT: 'clipboard-getCurrent',
} as const;

// 剪贴板历史窗口相关
export const CLIPBOARD_HISTORY = {
  SHOW: 'clipboard-history-show',
  HIDE: 'clipboard-history-hide',
  TOGGLE: 'clipboard-history-toggle',
  SELECT_ITEM: 'clipboard-history-select-item',
  GET_HISTORY: 'clipboard-history-get-history',
  CLEAR_HISTORY: 'clipboard-history-clear-history',
  UPDATE_STYLE: 'clipboard-history-update-style',
  GET_CURRENT_STYLE: 'clipboard-history-get-current-style',
  RESET: 'clipboard-window-reset',
  // 新增数据库功能
  GET_HISTORY_PAGINATED: 'clipboard-history-get-history-paginated',
  DELETE_ITEM: 'clipboard-history-delete-item',
  DELETE_ITEMS: 'clipboard-history-delete-items',
  SET_FAVORITE: 'clipboard-history-set-favorite',
  UPDATE_MEMO: 'clipboard-history-update-memo',
  UPDATE_TAGS: 'clipboard-history-update-tags',
  PURGE_DELETED: 'clipboard-history-purge-deleted',
} as const;

// 配置相关
export const SETTINGS = {
  GET: 'settings-get',
  UPDATE: 'settings-update',
} as const;

// 主题相关
export const THEME = {
  CHANGED: 'theme-changed',
} as const;

// 语言相关
export const LANGUAGE = {
  CHANGED: 'language-changed',
} as const;

// 剪贴板设置相关
export const CLIPBOARD_SETTINGS = {
  CHANGED: 'clipboard-settings-changed',
} as const;

// 用户状态相关
export const USER_STATUS = {
  CHANGED: 'user-status-changed',
} as const;

// ================= IPC 类型定义 =================

// 基础类型
export interface AppInfo {
  name: string;
  version: string;
}

export interface HotkeyInfo {
  activation: string;
  hide: string;
  devTools: string;
}

// 浮动球API
export interface FloatingBallAPI {
  drag: (delta: { x: number, y: number }) => void;
  dragStart: (mousePosition: { x: number, y: number }) => void;
  dragMove: (mousePosition: { x: number, y: number }) => void;
  dragEnd: () => void;
  click: () => void;
  doubleClick: () => void;
  longPress: () => void;
  onDraggingChange: (callback: (isDragging: boolean) => void) => () => void;
  onSnapSideChange: (callback: (side: 'left' | 'right') => void) => () => void;
  show: () => void;
  hide: () => void;
  toggle: () => void;
  setIgnoreMouseEvents?: (ignore: boolean, region?: { x: number, y: number, width: number, height: number }) => void;
  captureScreenStart?: (position: { x: number, y: number }) => void;
  captureScreenEnd?: (imageBase64: string) => void;
  cancelCapture?: () => void;
  onScreenCaptureChange?: (callback: (isCapturing: boolean) => void) => () => void;
  startVoiceInput?: () => void;
  endVoiceInput?: (voiceBase64: ArrayBuffer) => void;
  onVoiceRecordingChange?: (callback: (isRecording: boolean, volume?: number) => void) => () => void;
  onScreenCaptureImage?: (callback: (data: { imageData: string }) => void) => () => void;
  // 多屏截图相关
  getDisplays?: () => Promise<any[]>;
  startMultiScreenCapture?: () => void;
  selectDisplayCapture?: (displayId: string) => void;
}

// 应用API
export interface AppsAPI {
  launch: (appId: string) => Promise<boolean>;
  getAppList: () => Promise<any[]>;
}

// 文件API
export interface FilesAPI {
  getRecent: () => Promise<any[]>;
  search: (query: string) => Promise<any[]>;
  open: (path: string) => Promise<boolean>;
  openLocation: (path: string) => Promise<boolean>;
  readFilePreview: (path: string) => Promise<string>;
  checkExists: (path: string) => Promise<boolean>;
  selectFolder: () => Promise<string | null>;
}

// 数据库API
export interface DBAPI {
  all<T>(sql: string, params?: any): Promise<T[]>;
  get<T>(sql: string, id: any): Promise<T>;
  run(sql: string, params: any): Promise<boolean>;
  transaction(tasks: { sql: string; params: any[] }[]): Promise<boolean>;
}

// MCP API
export interface MCPAPI {
  listTools: () => Promise<any>;
  getAllTools: () => Promise<any>;
  callTool: (tool: { client: string; name: string; args: any }) => Promise<any>;
  getConfig: () => Promise<any>;
  putConfig: (config: any) => Promise<any>;
  activate: (args: { key: string; server: any; command?: string; args?: string[]; env?: Record<string, string> }) => Promise<any>;
  deactivated: (key: string) => Promise<any>;
  addServer: (key: string, server: any) => Promise<any>;
  updateServer: (key: string, server: any) => Promise<any>;
  deleteServer: (key: string) => Promise<any>;
  getMcpToolById: (id: string) => Promise<any>; // 添加根据ID获取MCP工具的方法
  onToolsUpdated: (callback: () => void) => () => void;
  onToolDownloadRequest: (callback: (data: any) => void) => () => void;
}

// 加密API
export interface CryptoAPI {
  hmacSha256Hex: (key: string, data: string) => Promise<string>;
}

// 系统信息API
export interface SystemAPI {
  getInfo: () => Promise<SystemInfo>;
  getComputerId: () => Promise<string>;
  getHomePath: () => Promise<string>;
  onThemeChanged: (callback: (theme: string) => void) => () => void;
  onLanguageChanged: (callback: (language: string) => void) => () => void;
  onClipboardSettingsChanged: (callback: (settings: any) => void) => () => void;
  onUserStatusChanged: (callback: (user: any) => void) => () => void;
}

// 系统信息类型定义
export interface SystemInfo {
  documentsPath: string;
  desktopPath: string;
  computerId: string;
  homePath: string;
  platform: string;
  arch: string;
  version: string;
  hostname: string;
  username: string;
  cpus: number;
  totalMemory: number;
  freeMemory: number;
  uptime: number;
  networkInterfaces: any;
}

// 存储API
export interface StoreAPI {
  get: (key: string, defaultValue?: any) => any;
  set: (key: string, val: any) => void;
}

// 认证API
export interface AuthAPI {
  getToken: () => Promise<string | null>;
  setToken: (token: string) => Promise<void>;
  clearToken: () => Promise<void>;
  onOpenLoginDialog: (callback: () => void) => () => void;
  notifyLoginSuccess: () => void;
  notifyLogout: () => void;
  broadcastUserStatusChange: (user: any) => Promise<boolean>;
  onClearAllTokens: (callback: () => void) => () => void;
}

// 剪贴板API
export interface ClipboardAPI {
  getHistory: () => Promise<any[]>;
  clearHistory: () => Promise<void>;
  onChange: (callback: (history: any[]) => void) => () => void;
  write: (clipboardItem: any) => void;
  getCurrent: () => Promise<any>;
}

// 剪贴板历史API
export interface ClipboardHistoryAPI {
  show: () => Promise<boolean>;
  hide: () => Promise<boolean>;
  toggle: () => Promise<boolean>;
  getHistory: () => Promise<any[]>;
  clearHistory: () => Promise<boolean>;
  selectItem: (item: any) => Promise<boolean>;
  updateStyle: (style: string) => Promise<boolean>;
  getCurrentStyle: () => Promise<string>;
  onReset: (callback: () => void) => () => void;
  // 新增数据库功能
  getHistoryPaginated: (options?: {
    page?: number;
    limit?: number;
    type?: string;
    favorite?: boolean;
    search?: string;
  }) => Promise<{
    items: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>;
  deleteItem: (id: string) => Promise<boolean>;
  deleteItems: (ids: string[]) => Promise<number>;
  setFavorite: (id: string, favorite: boolean) => Promise<boolean>;
  updateMemo: (id: string, memo: string) => Promise<boolean>;
  updateTags: (id: string, tags: string[]) => Promise<boolean>;
  purgeDeleted: (olderThanDays?: number) => Promise<number>;
}

// 窗口API
export interface WindowAPI {
  hideWindow: () => void;
  showWindow: () => void;
  toggleWindow: () => void;
  moveWindow: (mouseData: { mouseX: number, mouseY: number, width: number, height: number }) => void;
  openDevTools: () => void;
  onWindowShow: (callback: () => void) => () => void;
  onWindowHide: (callback: () => void) => () => void;
  onShowClipboardHistory: (callback: () => void) => () => void;
  // 新增API
  setResizable: (resizable: boolean) => void;
  isResizable: () => Promise<boolean>;
  setIgnoreMouseEvents: (ignore: boolean) => void;
  getConfig: () => Promise<any>;
  setPinned: (pinned: boolean) => void;
  isPinned: () => Promise<boolean>;
  // 设置窗口相关API
  showSettingsWindow: () => void;
  hideSettingsWindow: () => void;
  toggleSettingsWindow: () => void;
  setCompactMode: (compact: boolean) => void;
  // 内置功能快捷键触发监听器
  onSwitchToTranslator: (callback: () => void) => () => void;
  onSwitchToAiChat: (callback: () => void) => () => void;
  onSwitchToFileSearch: (callback: () => void) => () => void;
  onTriggerScreenshot: (callback: () => void) => () => void;
}

// 模型API
export interface PetAPI {
  getModels: (fileOrPath: string | File) => Promise<string[]>;
  onShowTip: (callback: (message: string, timeout?: number, priority?: number) => void) => () => void;
}

// 快捷键API
export interface ShortcutAPI {
  checkShortcutAvailable: (shortcut: string) => Promise<boolean>;
  registerAppShortcut: (id: string, shortcut: string, path: string, name: string) => Promise<boolean>;
  unregisterAppShortcut: (id: string) => Promise<boolean>;
  getAppShortcuts: () => Promise<Array<{id: string, shortcut: string, path: string, name: string}>>;
  disableAllShortcuts: () => Promise<boolean>;
  restoreAllShortcuts: () => Promise<boolean>;
  // 内置功能快捷键
  registerBuiltinShortcut: (id: string, shortcut: string, name: string) => Promise<boolean>;
  unregisterBuiltinShortcut: (id: string) => Promise<boolean>;
  getBuiltinShortcuts: () => Promise<Array<{id: string, shortcut: string, name: string}>>;
}

export interface SettingsAPI {
  get: () => Promise<any>;
  update: (newConfig: any) => Promise<boolean>;
}

// 代理配置接口
export interface ProxyConfig {
  mode: 'direct' | 'system' | 'fixed_servers' | 'pac_script';
  proxyRules?: string;
  proxyBypassRules?: string;
  pacScript?: string;
}

// 代理API
export interface ProxyAPI {
  getConfig: () => Promise<ProxyConfig | null>;
  setConfig: (config: ProxyConfig) => Promise<{ success: boolean }>;
  testConnection: (testUrl?: string) => Promise<boolean>;
  resetToSystem: () => Promise<{ success: boolean }>;
  setDirect: () => Promise<{ success: boolean }>;
  setManual: (proxyRules: string, bypassRules?: string) => Promise<{ success: boolean }>;
  setPac: (pacScript: string) => Promise<{ success: boolean }>;
}


// ================= 跨窗口通信相关 =================

// 跨窗口通信常量
export const CROSS_WINDOW = {
  EMIT: 'cross-window-emit',
  ON: 'cross-window-on',
  OFF: 'cross-window-off',
  BROADCAST: 'cross-window-broadcast',
  FORWARD: 'cross-window-forward',
} as const;

// 窗口类型枚举
export enum WindowType {
  MAIN = 'main',
  SETTINGS = 'settings',
  CLIPBOARD_HISTORY = 'clipboard-history',
  FLOATING_BALL = 'floating-ball',
  SCREEN_CAPTURE = 'screen-capture',
  PET = 'pet',
}

// 跨窗口事件数据接口
export interface CrossWindowEventData<T = any> {
  eventName: string;
  data: T;
  sourceWindow: WindowType;
  targetWindow?: WindowType | WindowType[]; // 目标窗口，undefined 表示广播到所有窗口
  timestamp: number;
  id: string; // 事件唯一标识符
}

// 跨窗口事件监听器类型
export type CrossWindowEventListener<T = any> = (data: T, sourceWindow: WindowType, eventId: string) => void;

// 跨窗口通信API
export interface CrossWindowAPI {
  // 发送事件到指定窗口或广播到所有窗口
  emit: <T = any>(eventName: string, data: T, targetWindow?: WindowType | WindowType[]) => Promise<boolean>;

  // 监听跨窗口事件
  on: <T = any>(eventName: string, listener: CrossWindowEventListener<T>) => () => void;

  // 移除事件监听器
  off: (eventName: string, listener?: CrossWindowEventListener) => void;

  // 广播事件到所有窗口（除了发送者）
  broadcast: <T = any>(eventName: string, data: T) => Promise<boolean>;

  // 获取当前窗口类型
  getCurrentWindowType: () => WindowType;

  // 获取所有活跃窗口类型
  getActiveWindows: () => Promise<WindowType[]>;
}

// 主体Electron API接口
export interface ElectronAPI {
  // 窗口相关API
  window: WindowAPI;
  // 应用信息
  appInfo: AppInfo;
  // 浮动球相关API
  floatingBall: FloatingBallAPI;
  // 应用程序相关API
  apps: AppsAPI;
  // 文件相关API
  files: FilesAPI;
  // 数据库相关API
  db: DBAPI;
  // MCP相关API
  mcp: MCPAPI;
  // 加密相关API
  crypto: CryptoAPI;
  // 系统信息相关API
  system: SystemAPI;
  // 存储相关API
  store: StoreAPI;
  // 认证相关API
  auth: AuthAPI;
  // 剪贴板相关API
  clipboard: ClipboardAPI;
  // 剪贴板历史相关API
  clipboardHistory: ClipboardHistoryAPI;
  // 模型API
  pet: PetAPI;
  // 代理相关API
  proxy: ProxyAPI;
  // 快捷键相关API
  shortcut: ShortcutAPI;
  // 设置相关API
  settings: SettingsAPI;
  // 跨窗口通信API
  crossWindow: CrossWindowAPI;
}