import { ipc<PERSON><PERSON><PERSON> } from 'electron';
// Import the specific API interface type
import { APP,AppsAPI } from '../../shared/ipc';

/**
 * 应用程序管理API
 */
// Ensure the exported object conforms to the AppsAPI type
export const appsAPI: AppsAPI = {
  // 启动应用
  launch: async (appPath: string): Promise<boolean> => {
    return await ipcRenderer.invoke(APP.LAUNCH, appPath);
  },

  // 获取应用列表
  getAppList: async () => {
    return await ipcRenderer.invoke(APP.GET_APP_LIST);
  }
}; 