import { ipc<PERSON><PERSON><PERSON> } from 'electron';
// Import the specific API interface type
import { FloatingBallAPI, FLOATING_BALL, SCREEN_CAPTURE, VOICE_INPUT } from '../../shared/ipc';

/**
 * 浮动球相关功能
 */
// Ensure the exported object conforms to the FloatingBallAPI type
export const floatingBallAPI: FloatingBallAPI = {
  // 旧方法保留以便兼容
  drag: (delta: { x: number, y: number }) => {
    ipcRenderer.send(FLOATING_BALL.DRAG, delta);
  },
  
  // 新的精确拖拽方法
  dragStart: (mousePosition: { x: number, y: number }) => {
    ipcRenderer.send(FLOATING_BALL.DRAG_START, mousePosition);
  },
  
  dragMove: (mousePosition: { x: number, y: number }) => {
    ipcRenderer.send(FLOATING_BALL.DRAG_MOVE, mousePosition);
  },
  
  dragEnd: () => {
    ipcRenderer.send(FLOATING_BALL.DRAG_END);
  },
  
  click: () => {
    ipcRenderer.send(FLOATING_BALL.CLICK);
  },
  
  doubleClick: () => {
    ipcRenderer.send(FLOATING_BALL.DOUBLE_CLICK);
  },
  
  longPress: () => {
    // 长按现在主要用于触发语音输入
    ipcRenderer.send(VOICE_INPUT.START);
  },
  
  // 监听拖拽状态变化
  onDraggingChange: (callback: (isDragging: boolean) => void) => {
    const wrappedCallback = (_: any, isDragging: boolean) => callback(isDragging);
    ipcRenderer.on(FLOATING_BALL.DRAGGING, wrappedCallback);
    
    return () => {
      ipcRenderer.removeListener(FLOATING_BALL.DRAGGING, wrappedCallback);
    };
  },
  
  // 监听吸附侧边变化
  onSnapSideChange: (callback: (side: 'left' | 'right') => void) => {
    const wrappedCallback = (_: any, side: 'left' | 'right') => callback(side);
    ipcRenderer.on(FLOATING_BALL.SNAP_SIDE_CHANGE, wrappedCallback);
    
    return () => {
      ipcRenderer.removeListener(FLOATING_BALL.SNAP_SIDE_CHANGE, wrappedCallback);
    };
  },
  
  // 设置鼠标事件穿透状态
  setIgnoreMouseEvents: (ignore: boolean, region?: { x: number, y: number, width: number, height: number }) => {
    ipcRenderer.send(FLOATING_BALL.SET_IGNORE_MOUSE, {
      ignore,
      ...(region || {})
    });
  },
  
  // 截图相关功能
  captureScreenStart: (position: { x: number, y: number }) => {
    ipcRenderer.send(SCREEN_CAPTURE.START, position);
  },

  captureScreenEnd: (imageBase64: string) => {
    ipcRenderer.send(SCREEN_CAPTURE.END, imageBase64);
  },

  cancelCapture: () => {
    ipcRenderer.send(SCREEN_CAPTURE.CANCEL);
  },

  // 多屏截图相关功能
  getDisplays: async () => {
    return await ipcRenderer.invoke(SCREEN_CAPTURE.GET_DISPLAYS);
  },

  startMultiScreenCapture: () => {
    ipcRenderer.send(SCREEN_CAPTURE.START_MULTI);
  },

  selectDisplayCapture: (displayId: string) => {
    ipcRenderer.send(SCREEN_CAPTURE.SELECT_DISPLAY, displayId);
  },
  
  // 监听截图状态变化
  onScreenCaptureChange: (callback: (isCapturing: boolean) => void) => {
    const wrappedCallback = (_: any, isCapturing: boolean) => callback(isCapturing);
    ipcRenderer.on(SCREEN_CAPTURE.CHANGE, wrappedCallback);
    
    return () => {
      ipcRenderer.removeListener(SCREEN_CAPTURE.CHANGE, wrappedCallback);
    };
  },
  
  // 监听截图图像数据
  onScreenCaptureImage: (callback: (data: { imageData: string }) => void): (() => void) => {
    const wrappedCallback = (_: any, data: { imageData: string }) => callback(data);
    ipcRenderer.on(SCREEN_CAPTURE.IMAGE, wrappedCallback);
    
    return () => {
      ipcRenderer.removeListener(SCREEN_CAPTURE.IMAGE, wrappedCallback);
    };
  },
  
  // 语音输入相关功能
  startVoiceInput: () => {
    // 调用主进程开始语音输入
    console.log('浮动球客户端API: 开始语音输入');
    ipcRenderer.send(VOICE_INPUT.START);
  },
  
  endVoiceInput: (voiceBase64: ArrayBuffer) => {
    // 调用主进程结束语音输入
    console.log('浮动球客户端API: 结束语音输入');
    ipcRenderer.send(VOICE_INPUT.END, voiceBase64);
  },
  
  // 监听语音输入状态变化
  onVoiceRecordingChange: (callback: (isRecording: boolean, volume?: number) => void) => {
    const wrappedCallback = (_: any, isRecording: boolean, volume?: number) => callback(isRecording, volume);
    ipcRenderer.on(VOICE_INPUT.CHANGE, wrappedCallback);
    
    return () => {
      ipcRenderer.removeListener(VOICE_INPUT.CHANGE, wrappedCallback);
    };
  },
  
  // 悬浮球显示控制
  show: () => {
    ipcRenderer.send(FLOATING_BALL.SHOW);
  },
  
  hide: () => {
    ipcRenderer.send(FLOATING_BALL.HIDE);
  },
  
  toggle: () => {
    ipcRenderer.send(FLOATING_BALL.TOGGLE);
  }
}; 