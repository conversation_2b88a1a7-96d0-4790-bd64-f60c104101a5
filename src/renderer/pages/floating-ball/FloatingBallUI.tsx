import React, { forwardRef, useEffect, useState, useRef } from 'react';
import './FloatingBall.css'; // 导入新创建的CSS样式
import { NotificationData } from '../../../shared/types/notification';

interface FloatingBallUIProps {
  // 外部状态
  isCapturing?: boolean;
  isVoiceRecording?: boolean;
  voiceVolume?: number;
  snapSide?: 'left' | 'right';
  // 事件回调
  onSingleClick?: () => void;
  onDoubleClick?: () => void;
  onLongPress?: () => void;
  onDragStart?: (e: React.MouseEvent) => void;
  onDragEnd?: () => void;
  onDrag?: (e: MouseEvent) => void;
  // 截图模式结束回调
  onCaptureEnd?: () => void;
  // 鼠标悬停和离开事件
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  // 拖放内容处理回调
  onExternalDrop?: (content: {
    type: 'text' | 'file' | 'image' | 'directory',
    data: string | File[] | { path: string, isDirectory: boolean }[]
  }) => void;
  // 外部拖拽进入和离开事件
  onExternalDragEnter?: () => void;
  onExternalDragLeave?: () => void;
}

/**
 * 浮动球UI组件 - 重构版
 * 纯UI组件，负责渲染浮动球的样式和事件处理
 */
export const FloatingBallUI = forwardRef<HTMLDivElement, FloatingBallUIProps>(
  ({ 
    isCapturing = false, 
    isVoiceRecording = false, 
    voiceVolume = 0,
    snapSide = 'right',
    onSingleClick,
    onDoubleClick,
    onLongPress,
    onDragStart,
    onDragEnd,
    onDrag,
    onCaptureEnd,
    onMouseEnter,
    onMouseLeave,
    onExternalDrop,
    onExternalDragEnter,
    onExternalDragLeave
  }, ref) => {
      // 内部状态
  const [isDragging, setIsDragging] = useState(false);
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [audioWaves, setAudioWaves] = useState<Array<{id: number}>>([]);
  const [waveId, setWaveId] = useState(0);
  // 拖拽悬停状态
  const [isDragOver, setIsDragOver] = useState(false);
  // 拖拽类型
  const [dragContentType, setDragContentType] = useState<'unknown' | 'text' | 'file' | 'image' | 'directory'>('unknown');
  // 鼠标悬停状态
  const [isHovered, setIsHovered] = useState(false);
  // 通知状态
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
    
    // 事件状态引用
    const mouseStateRef = useRef({
      isMouseDown: false,        // 鼠标是否按下
      isLongPress: false,        // 是否触发了长按
      isDragging: false,         // 是否正在拖拽
      isDoubleClickPending: false, // 是否正在等待双击的第二次点击
      startX: 0,                 // 鼠标按下时的X坐标
      startY: 0,                 // 鼠标按下时的Y坐标
      clickCount: 0,             // 点击计数
      lastClickTime: 0,          // 上次点击时间
      hasHandledRelease: false,  // 是否已经处理过释放事件
      lastEventType: ''          // 上一个事件类型（用于调试和防止多次触发）
    });
    
    // 定时器引用
    const timersRef = useRef({
      longPressTimer: null as NodeJS.Timeout | null,
      doubleClickTimer: null as NodeJS.Timeout | null,
      singleClickTimer: null as NodeJS.Timeout | null,
      captureTimer: null as NodeJS.Timeout | null,
      preventClickTimer: null as NodeJS.Timeout | null  // 防止点击的定时器
    });
    
    // 常量
    const LONG_PRESS_DELAY = 500;     // 长按触发时间（毫秒）
    const DOUBLE_CLICK_DELAY = 200;   // 双击间隔时间（毫秒）- 优化响应速度
    const MOVE_THRESHOLD = 5;         // 移动阈值（像素）
    const PREVENT_CLICK_DELAY = 50;   // 长按/拖拽后防止点击的延迟（毫秒），降低到更短的时间
    
    // 清理所有定时器
    const clearAllTimers = () => {
      if (timersRef.current.longPressTimer) {
        clearTimeout(timersRef.current.longPressTimer);
        timersRef.current.longPressTimer = null;
      }
      if (timersRef.current.doubleClickTimer) {
        clearTimeout(timersRef.current.doubleClickTimer);
        timersRef.current.doubleClickTimer = null;
      }
      if (timersRef.current.singleClickTimer) {
        clearTimeout(timersRef.current.singleClickTimer);
        timersRef.current.singleClickTimer = null;
      }
      if (timersRef.current.captureTimer) {
        clearTimeout(timersRef.current.captureTimer);
        timersRef.current.captureTimer = null;
      }
      if (timersRef.current.preventClickTimer) {
        clearTimeout(timersRef.current.preventClickTimer);
        timersRef.current.preventClickTimer = null;
      }
    };

    // 通知处理函数
    const handleNotificationShow = (data: NotificationData) => {
      setNotifications(prev => [...prev, data]);
      
      // 自动隐藏通知
      if (data.duration && data.duration > 0) {
        setTimeout(() => {
          handleNotificationHide(data.id);
        }, data.duration);
      }
    };

    const handleNotificationHide = (id: string) => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    };

    const handleNotificationClear = () => {
      setNotifications([]);
    };

    // 监听通知事件
    useEffect(() => {
      const handleShow = (data: NotificationData) => {
        handleNotificationShow(data);
      };

      const handleHide = (id?: string) => {
        if (id) {
          handleNotificationHide(id);
        }
      };

      const handleClear = () => {
        handleNotificationClear();
      };

      // 使用新的跨窗口通信服务监听通知事件
      const unsubscribeShow = window.electron.crossWindow.on('notification:show', handleShow);
      const unsubscribeHide = window.electron.crossWindow.on('notification:hide', handleHide);
      const unsubscribeClear = window.electron.crossWindow.on('notification:clear', handleClear);

      return () => {
        unsubscribeShow();
        unsubscribeHide();
        unsubscribeClear();
      };
    }, []);
    
    // 记录事件类型，用于调试和防止事件冲突
    const logEventType = (eventType: string) => {
      console.log(`事件类型: ${eventType}`, {
        isLongPress: mouseStateRef.current.isLongPress,
        isDragging: mouseStateRef.current.isDragging,
        isVoiceRecording,
        hasHandledRelease: mouseStateRef.current.hasHandledRelease
      });
      mouseStateRef.current.lastEventType = eventType;
    };
    
    // 监听截图状态变化 - 在截图模式下设置自动退出定时器
    useEffect(() => {
      if (isCapturing) {
        console.log('悬浮球: 进入截图模式，悬浮球隐藏');
      } else {
        console.log('悬浮球: 退出截图模式，悬浮球显示');
        
        // 如果退出截图模式，清除相关定时器
        if (timersRef.current.captureTimer) {
          clearTimeout(timersRef.current.captureTimer);
          timersRef.current.captureTimer = null;
        }
      }

      return () => {
        // 组件卸载时清理定时器
        if (timersRef.current.captureTimer) {
          clearTimeout(timersRef.current.captureTimer);
          timersRef.current.captureTimer = null;
        }
      };
    }, [isCapturing, onCaptureEnd]);

    // 监听外部录音状态变化
    useEffect(() => {
      // 当录音状态变化时，同步更新内部状态
      console.log('录音状态变化:', { isVoiceRecording });
      
      if (isVoiceRecording) {
        // 进入录音状态，确保不能拖拽
        const mouseState = mouseStateRef.current;
        mouseState.isDragging = false;
        setIsDragging(false);
        mouseState.isLongPress = true; // 设置为长按状态，防止触发拖拽
        setIsLongPressing(true);
        
        // 确保添加鼠标抬起事件监听，以便能结束录音
        document.addEventListener('mouseup', handleMouseUp);
      } else {
        // 退出录音状态
        if (isLongPressing) {
          setIsLongPressing(false);
          const mouseState = mouseStateRef.current;
          mouseState.isLongPress = false;
        }
        
        // 清除所有可能的标记
        mouseStateRef.current.hasHandledRelease = false;
      }
      
      // 不在这里移除事件监听器，以避免干扰正常的事件处理
    }, [isVoiceRecording]);

    // 设置防止点击的锁定状态
    const setPreventClick = () => {
      mouseStateRef.current.hasHandledRelease = true;
      
      // 设置一个短暂的锁定期，防止在长按或拖拽后立即触发点击
      if (timersRef.current.preventClickTimer) {
        clearTimeout(timersRef.current.preventClickTimer);
      }
      
      timersRef.current.preventClickTimer = setTimeout(() => {
        mouseStateRef.current.hasHandledRelease = false;
        timersRef.current.preventClickTimer = null;
      }, PREVENT_CLICK_DELAY);
    };
    
    // 处理鼠标移动事件
    const handleMouseMove = (e: MouseEvent) => {
      const mouseState = mouseStateRef.current;
      
      // 确保鼠标已按下且未触发长按，并且不在录音状态
      if (!mouseState.isMouseDown || mouseState.isLongPress || isVoiceRecording) {
        console.log('忽略鼠标移动', { isLongPress: mouseState.isLongPress, isVoiceRecording });
        return;
      }
      
      // 计算移动距离
      const deltaX = Math.abs(e.clientX - mouseState.startX);
      const deltaY = Math.abs(e.clientY - mouseState.startY);
      
      // 如果移动超过阈值且未开始拖拽，则开始拖拽
      if ((deltaX > MOVE_THRESHOLD || deltaY > MOVE_THRESHOLD) && !mouseState.isDragging) {
        // 再次确认不在录音状态
        if (isVoiceRecording) {
          console.log('在录音状态下阻止拖拽开始');
          return;
        }
        
        mouseState.isDragging = true;
        setIsDragging(true);
        logEventType('drag_start');
        
        // 清除长按定时器
        if (timersRef.current.longPressTimer) {
          clearTimeout(timersRef.current.longPressTimer);
          timersRef.current.longPressTimer = null;
        }
        
        // 调用拖拽开始回调
        if (onDragStart) {
          onDragStart(e as any);
        }
      }
      
      // 如果正在拖拽且不在录音状态，触发拖拽回调
      if (mouseState.isDragging && onDrag && !isVoiceRecording) {
        onDrag(e);
      }
    };
    
    // 处理鼠标抬起事件
    const handleMouseUp = (e: MouseEvent) => {
      const mouseState = mouseStateRef.current;
      
      console.log('鼠标抬起事件', { 
        isLongPress: mouseState.isLongPress, 
        isDragging: mouseState.isDragging,
        isVoiceRecording,
        hasHandledRelease: mouseState.hasHandledRelease 
      });
      
      // 如果已经处理过释放事件且不在录音状态，直接返回
      // 但在录音状态下，我们需要允许处理释放事件，以便能结束录音
      if (mouseState.hasHandledRelease && !isVoiceRecording) {
        console.log('已处理过释放事件，忽略');
        return;
      }
      
      // 标记鼠标已抬起
      mouseState.isMouseDown = false;
      
      // 清除长按定时器
      if (timersRef.current.longPressTimer) {
        clearTimeout(timersRef.current.longPressTimer);
        timersRef.current.longPressTimer = null;
      }
      
      // 处理录音状态下的鼠标抬起
      if (isVoiceRecording) {
        logEventType('voice_recording_end');
        
        // 重置双击状态
        mouseState.isDoubleClickPending = false;
        if (timersRef.current.doubleClickTimer) {
          clearTimeout(timersRef.current.doubleClickTimer);
          timersRef.current.doubleClickTimer = null;
        }
        
        // 重置拖拽状态
        mouseState.isDragging = false;
        setIsDragging(false);
        
        // 调用长按松开回调来结束录音
        if (onLongPress) {
          console.log('结束录音');
          onLongPress();
        }
        
        // 标记已处理，但不设置防止点击的锁定
        // 这样可以确保录音结束后能立即响应新的事件
        mouseState.hasHandledRelease = true;
        mouseState.lastClickTime = Date.now();
        
        // 移除事件监听
        document.removeEventListener('mousemove', handleMouseMove);
        return; // 不移除mouseup事件监听器，以便处理后续的mouseup
      }
      
      // 处理不同类型的事件，优先级：拖拽 > 长按 > 点击
      
      // 1. 处理拖拽释放
      if (mouseState.isDragging) {
        logEventType('drag_end');
        mouseState.isDragging = false;
        setIsDragging(false);
        
        // 拖拽结束后重置悬停状态，因为鼠标可能已经不在悬浮球范围内
        setIsHovered(false);
        
        if (onDragEnd) {
          onDragEnd();
        }
        
        // 重置双击状态，确保拖拽后的点击不会被认为是双击的一部分
        mouseState.isDoubleClickPending = false;
        // 清除双击定时器
        if (timersRef.current.doubleClickTimer) {
          clearTimeout(timersRef.current.doubleClickTimer);
          timersRef.current.doubleClickTimer = null;
        }
        
        // 设置防止点击的锁定
        setPreventClick();
      } 
      // 2. 处理长按释放
      else if (mouseState.isLongPress) {
        logEventType('long_press_end');
        mouseState.isLongPress = false;
        setIsLongPressing(false);
        
        // 重置双击状态，确保长按后的点击不会被认为是双击的一部分
        mouseState.isDoubleClickPending = false;
        // 清除双击定时器
        if (timersRef.current.doubleClickTimer) {
          clearTimeout(timersRef.current.doubleClickTimer);
          timersRef.current.doubleClickTimer = null;
        }
        
        // 设置防止点击的锁定
        setPreventClick();
      } 
      // 3. 处理点击
      else {
        logEventType('click');
        handleClick();
      }
      
      // 移除事件监听
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    // 组件挂载/卸载处理
    useEffect(() => {
      // 音频波动动画效果
      let waveInterval: NodeJS.Timeout | null = null;
      
      if (isVoiceRecording || isLongPressing) {
        waveInterval = setInterval(() => {
          const newWaveId = waveId + 1;
          setWaveId(newWaveId);
          setAudioWaves(prev => [...prev, {id: newWaveId}]);
          
          // 清理旧的波动效果
          setTimeout(() => {
            setAudioWaves(prev => prev.filter(wave => wave.id !== newWaveId));
          }, 1500);
        }, 300);
      } 
      
      // 组件卸载清理
      return () => {
        clearAllTimers();
        if (waveInterval) clearInterval(waveInterval);
        
        // 移除事件监听器
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }, [isVoiceRecording, isLongPressing, waveId]);
    
    // 处理鼠标按下事件
    const handleMouseDown = (e: React.MouseEvent) => {
      // 只处理左键点击
      if (e.button !== 0) return;
      
      e.preventDefault();
      e.stopPropagation();
      
      // 如果在防止点击的锁定期内，直接返回
      if (mouseStateRef.current.hasHandledRelease && 
          Date.now() - mouseStateRef.current.lastClickTime < 100) { // 只在距离上次事件非常短的时间内阻止
        console.log('在防止点击锁定期内，忽略鼠标按下事件');
        return;
      }
      
      // 如果已经在录音状态，直接处理结束录音逻辑，不进入拖拽流程
      if (isVoiceRecording) {
        logEventType('voice_recording_stop');
        
        // 重置这个标记，确保可以处理后续的mouseup事件
        mouseStateRef.current.hasHandledRelease = false;
        
        if (onLongPress) {
          console.log('直接结束录音');
          onLongPress();
        }
        
        return;
      }
      
      // 重置鼠标状态
      const mouseState = mouseStateRef.current;
      mouseState.isMouseDown = true;
      mouseState.isLongPress = false;
      mouseState.isDragging = false;
      mouseState.hasHandledRelease = false; // 重置已处理标志
      mouseState.startX = e.clientX;
      mouseState.startY = e.clientY;
      mouseState.lastClickTime = Date.now(); // 更新最后事件时间
      
      // 清除所有可能的定时器
      clearAllTimers();
      
      logEventType('mouse_down');
      
      // 设置长按定时器
      timersRef.current.longPressTimer = setTimeout(() => {
        // 如果鼠标仍然按下且没有拖动，触发长按
        if (mouseState.isMouseDown && !mouseState.isDragging) {
          logEventType('long_press_start');
          mouseState.isLongPress = true;
          setIsLongPressing(true);
          
          // 调用长按回调开始录音
          if (onLongPress && !isVoiceRecording) {
            console.log('开始录音');
            onLongPress();
          }
        }
        timersRef.current.longPressTimer = null;
      }, LONG_PRESS_DELAY);
      
      // 添加全局事件监听
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };
    
    // 处理点击事件
    const handleClick = () => {
      // 如果在防止点击的锁定期内，忽略点击
      if (mouseStateRef.current.hasHandledRelease) {
        console.log('在防止点击锁定期内，忽略点击');
        return;
      }
      
      const mouseState = mouseStateRef.current;
      const now = Date.now();
      
      // 判断上次操作是否是长按或拖拽
      const wasLongPressOrDrag = mouseState.lastEventType === 'long_press_end' ||
                                mouseState.lastEventType === 'drag_end' ||
                                mouseState.lastEventType === 'global_mouse_up';
      
      // 如果上次操作是长按或拖拽，则强制重置双击状态
      if (wasLongPressOrDrag) {
        mouseState.isDoubleClickPending = false;
        console.log('重置双击状态，上次操作为:', mouseState.lastEventType);
      }
      
      // 判断是否是双击
      if (mouseState.isDoubleClickPending) {
        logEventType('double_click');
        
        // 清除定时器
        if (timersRef.current.doubleClickTimer) {
          clearTimeout(timersRef.current.doubleClickTimer);
          timersRef.current.doubleClickTimer = null;
        }
        if (timersRef.current.singleClickTimer) {
          clearTimeout(timersRef.current.singleClickTimer);
          timersRef.current.singleClickTimer = null;
        }
        
        // 重置双击状态
        mouseState.isDoubleClickPending = false;
        
        // 触发双击回调
        if (onDoubleClick) {
          onDoubleClick();
        }
        
        // 设置防止连续点击
        setPreventClick();
      } 
      // 第一次点击
      else {
        logEventType('single_click_pending');
        mouseState.isDoubleClickPending = true;
        
        // 设置双击等待定时器
        timersRef.current.doubleClickTimer = setTimeout(() => {
          // 时间到了仍未收到第二次点击，判定为单击
          if (mouseState.isDoubleClickPending) {
            logEventType('single_click_confirmed');
            mouseState.isDoubleClickPending = false;
            
            // 触发单击回调
            if (onSingleClick) {
              onSingleClick();
            }
          }
          timersRef.current.doubleClickTimer = null;
        }, DOUBLE_CLICK_DELAY);
      }
      
      // 更新最后点击时间
      mouseState.lastClickTime = now;
    };
    
    // 处理拖拽进入
    const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      
      // 判断拖拽的内容类型
      const dt = e.dataTransfer;
      
      // 设置拖拽悬停状态
      setIsDragOver(true);
      
      // 判断拖拽内容类型
      if (dt.types.includes('Files')) {
        // 有文件，进一步判断是图片还是普通文件
        if (dt.items && dt.items.length > 0) {
          // 检查第一项的类型
          const firstItem = dt.items[0];
          if (firstItem.kind === 'file') {
            // 这里只能粗略判断，因为还没有真正获取到文件
            if (firstItem.type && firstItem.type.startsWith('image/')) {
              setDragContentType('image');
            } else {
              // 可能是文件或目录，但目录信息在这个阶段无法获取
              setDragContentType('file');
            }
          }
        }
      } else if (dt.types.includes('text/plain') || dt.types.includes('text/html')) {
        setDragContentType('text');
      } else {
        setDragContentType('unknown');
      }
      
      console.log('拖拽进入悬浮球', { type: dragContentType });
      
      // 通知父组件拖拽进入
      if (onExternalDragEnter) {
        onExternalDragEnter();
      }
    };
    
    // 处理拖拽离开
    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      
      // 重置拖拽状态
      setIsDragOver(false);
      setDragContentType('unknown');
      
      console.log('拖拽离开悬浮球');
      
      // 通知父组件拖拽离开
      if (onExternalDragLeave) {
        onExternalDragLeave();
      }
    };
    
    // 处理拖拽悬停（必须阻止默认行为，否则无法触发drop）
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
    };
    
    // 处理拖拽释放
    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      
      // 重置拖拽状态
      setIsDragOver(false);
      
      console.log('内容被拖放到悬浮球上');
      
      // 获取拖拽的数据
      const dt = e.dataTransfer;
      
      // 处理文件拖放
      if (dt.files && dt.files.length > 0) {
        const files = Array.from(dt.files);
        
        // 检查是否为图片
        const isImage = files.some(file => file.type && file.type.startsWith('image/'));
        
        // 如果是图片
        if (isImage) {
          console.log('拖放的是图片文件:', files.map(f => f.name));
          
          if (onExternalDrop) {
            onExternalDrop({
              type: 'image',
              data: files
            });
          } else {
            // 弹出简单提示
            // 实际使用时应该实现更好的UI交互
            alert(`拖放了 ${files.length} 个图片文件:\n${files.map(f => f.name).join('\n')}\n\n请选择要进行的操作`);
          }
        }
        // 如果是普通文件或目录
        else {
          // 区分文件和目录
          const fileAndDirs = files.map(file => {
            // 在渲染进程中，判断目录比较困难
            // 通常目录的size为0且没有type，但这不是绝对的
            // 在实际项目中，可能需要通过主进程或Node.js API来确定
            const isDirectory = !file.type && (file as any).size === 0;
            return {
              path: (file as any).path, // Electron环境下文件会有path属性
              name: file.name,
              isDirectory: isDirectory
            };
          });
          
          console.log('拖放的是文件或目录:', fileAndDirs);
          
          if (onExternalDrop) {
            onExternalDrop({
              type: 'file',
              data: fileAndDirs
            });
          } else {
            // 弹出简单提示
            alert(`拖放了文件/目录:\n${fileAndDirs.map(f => `${f.name} ${f.isDirectory ? '(目录)' : ''}`).join('\n')}\n\n请选择要进行的操作`);
          }
        }
      }
      // 处理文本拖放
      else if (dt.types.includes('text/plain')) {
        const text = dt.getData('text/plain');
        console.log('拖放的是文本:', text);
        
        if (onExternalDrop) {
          onExternalDrop({
            type: 'text',
            data: text
          });
        } else {
          // 弹出简单提示
          alert(`拖放了文本内容:\n${text.length > 100 ? text.substring(0, 100) + '...' : text}\n\n请选择要进行的操作`);
        }
      }
      // 处理HTML内容
      else if (dt.types.includes('text/html')) {
        const html = dt.getData('text/html');
        console.log('拖放的是HTML内容');
        
        if (onExternalDrop) {
          onExternalDrop({
            type: 'text',
            data: html
          });
        } else {
          // 弹出简单提示
          alert(`拖放了HTML内容，长度: ${html.length} 字符\n\n请选择要进行的操作`);
        }
      }
      // 其他未知类型
      else {
        console.log('拖放的是未知类型内容');
        
        // 尝试遍历所有可能的数据类型
        const availableTypes = dt.types;
        console.log('可用的数据类型:', availableTypes);
        
        alert('拖放了未知类型的内容，无法处理');
      }
      
      // 重置拖拽内容类型
      setDragContentType('unknown');
    };
    
      // 构建悬浮球的类名
  const getBallClassName = () => {
    // 基础类名 - 改为白色背景
    let className = 'floating-ball overflow-hidden bg-white';
    
    // 根据吸附方向添加对应类，保证CSS能准确渲染
    className += snapSide === 'left' ? ' floating-ball-left' : ' floating-ball-right';
    
    // 根据状态添加样式类
    if (!isDragging && !isVoiceRecording && !isLongPressing) {
      className += ' floating-ball-breath';
    }
    
    // 拖拽状态
    if (isDragging) {
      className += ' scale-95 opacity-90 rounded-full'; // 拖拽时恢复圆形
    } else {
      // 悬停状态或有通知时 - 拉长效果
      if (isHovered || notifications.length > 0) {
        className += snapSide === 'left' ? ' floating-ball-hovered-left' : ' floating-ball-hovered';
      }
    }
    
    // 拖拽悬停状态
    if (isDragOver) {
      className += ' ring-4 ring-green-400 scale-110 opacity-80';
    }
    
    // 截图模式
    if (isCapturing) {
      className += ' floating-ball-capturing';
    }
    
    // 语音录入模式
    if (isVoiceRecording || isLongPressing) {
      className += ' floating-ball-recording bg-red-500';
    }
    
    return className;
  };

    // 获取悬浮球宽度 - 有通知时也要拉长
    const getBallWidth = () => {
      if (isDragging) return '38px';
      if (isHovered || notifications.length > 0) {
        return notifications.length > 0 ? '150px' : '52px'; // 有通知时稍微宽一点
      }
      return '46px';
    };

    // 渲染通知内容（集成在悬浮球内部）
    const renderNotificationContent = () => {
      if (notifications.length === 0) return null;
      
      const latestNotification = notifications[notifications.length - 1]; // 显示最新的通知
      
      return (
        <div className="notification-content" style={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          height: '100%',
          padding: snapSide === 'right' ? '0 6px 0 4px' : '0 4px 0 6px',
          backgroundColor: getNotificationBgColor(latestNotification),
          borderRadius: 'inherit',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* 图标区域 */}
          <div className="notification-icon" style={{
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0,
            marginRight: snapSide === 'right' ? '6px' : '0px',
            marginLeft: snapSide === 'left' ? '6px' : '0px'
          }}>
            <img
              src="../../assets/images/icon_64x64.png"
              alt="Aido"
              className="object-contain rounded-full"
              style={{width: '32px', height: '32px'}}
              draggable={false}
            />
          </div>
          
          {/* 通知文本区域 */}
          <div className="notification-text" style={{
            flex: 1,
            color: latestNotification.type === 'info' ? '#000000' : 'white', // info类型用黑色文字
            overflow: 'hidden',
            minWidth: 0, // 确保能够正确收缩
            ...(snapSide === 'left' ? { order: -1, marginRight: '6px', marginLeft: '0px' } : {})
          }}>
            <div className="notification-title" style={{
              fontSize: '12px',
              fontWeight: '600',
              lineHeight: '1.1',
              marginBottom: '1px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {latestNotification.title}
            </div>
            <div className="notification-detail" style={{
              fontSize: '10px',
              opacity: 0.9,
              lineHeight: '1.1',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {latestNotification.detail}
            </div>
          </div>
          
          {/* 多通知指示器 */}
          {notifications.length > 1 && (
            <div className="notification-count" style={{
              position: 'absolute',
              top: '4px',
              right: snapSide === 'right' ? '4px' : 'auto',
              left: snapSide === 'left' ? '4px' : 'auto',
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              color: 'white',
              borderRadius: '10px',
              padding: '2px 6px',
              fontSize: '10px',
              fontWeight: 'bold',
              minWidth: '16px',
              textAlign: 'center'
            }}>
              {notifications.length}
            </div>
          )}
        </div>
      );
    };

    // 渲染截图图标
    const renderCropIcon = () => {
      return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M4 4h6c1.1 0 2 .9 2 2v12"></path>
          <path d="M20 16v4h-4"></path>
          <path d="M20 10V4h-6"></path>
          <path d="M12 12L20 20"></path>
          <path d="M12 12V8"></path>
          <rect x="9" y="14" width="2" height="4" fill="#FF4500"></rect>
        </svg>
      );
    };
    
    // 渲染麦克风图标
    const renderMicIcon = () => {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="white">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
        </svg>
      );
    };

    // 创建一个全局鼠标事件监听器
    useEffect(() => {
      // 只在录音状态下添加全局监听
      if (isVoiceRecording) {
        console.log('添加全局鼠标事件监听');
        
        // 确保在录音状态下不能拖拽
        const mouseState = mouseStateRef.current;
        mouseState.isDragging = false;
        setIsDragging(false);
        
        // 全局鼠标抬起处理函数
        const globalMouseUpHandler = (e: MouseEvent) => {
          console.log('全局鼠标抬起事件', { 
            isVoiceRecording,
            hasHandledRelease: mouseStateRef.current.hasHandledRelease 
          });
          
          // 即使已处理过释放事件，在录音状态下也需要处理
          if (isVoiceRecording && onLongPress) {
            logEventType('global_mouse_up');
            
            // 重置双击状态，确保长按后的点击不会被认为是双击的一部分
            mouseStateRef.current.isDoubleClickPending = false;
            // 清除双击定时器
            if (timersRef.current.doubleClickTimer) {
              clearTimeout(timersRef.current.doubleClickTimer);
              timersRef.current.doubleClickTimer = null;
            }
            
            // 重置拖拽状态
            mouseStateRef.current.isDragging = false;
            setIsDragging(false);
            
            // 结束录音
            onLongPress();
            
            // 标记已处理
            mouseStateRef.current.hasHandledRelease = true;
            mouseStateRef.current.lastClickTime = Date.now(); // 更新最后事件时间
            
            // 重置长按和录音状态
            mouseStateRef.current.isLongPress = false;
            setIsLongPressing(false);
          }
        };
        
        // 添加监听
        window.addEventListener('mouseup', globalMouseUpHandler);
        
        // 清理函数
        return () => {
          window.removeEventListener('mouseup', globalMouseUpHandler);
        };
      }
    }, [isVoiceRecording, onLongPress]);

    // 在截图模式下不渲染悬浮球
    if (isCapturing) {
      return null;
    }

    // 获取通知背景颜色
    const getNotificationBgColor = (notification: NotificationData): string => {
      switch (notification.type) {
        case 'success':
          return '#10B981'; // 绿色
        case 'fail':
          return '#EF4444'; // 红色
        case 'custom':
          return notification.bgColor || '#6B7280'; // 自定义颜色或灰色
        case 'info':
        default:
          return '#FFFFFF'; // 白色
      }
    };

    return (
      <div className="floating-ball-container" style={{ 
        position: 'absolute', 
        top: 0, 
        left: 0, 
        right: 0, 
        bottom: 0
      }}>
        <div
          ref={ref}
          className={getBallClassName()}
          onMouseDown={handleMouseDown}
          onMouseEnter={() => {
            setIsHovered(true);
            onMouseEnter?.();
          }}
          onMouseLeave={() => {
            setIsHovered(false);
            onMouseLeave?.();
          }}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          style={{
            width: getBallWidth(),
            height: '38px',
            cursor: isDragging ? 'grabbing' : 'grab',
            position: 'absolute',  // 使用absolute定位
            top: '18px',
            ...(snapSide === 'right' 
              ? { right: isDragging ? '28px' : '0px' } 
              : { left: isDragging ? '28px' : '0px' }),
            zIndex: 9999,       // 确保悬浮球在最上层
            // 拖拽时恢复圆形
            borderRadius: isDragging ? '50%' : undefined,
            // 拖动时不使用过渡动画，瞬间变化；其他时候保持平滑过渡
            transition: isDragging ? 'none' : 'all 0.3s ease',
            // 使用合适的对齐，避免宽度变化时的偏移
            transformOrigin: `${snapSide} center`
          }}
        >
          {/* 添加拖拽提示UI */}
          {isDragOver && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 rounded-full z-20">
              <div className="text-white text-xs font-bold">
                {dragContentType === 'text' ? '文本' : 
                 dragContentType === 'image' ? '图片' : 
                 dragContentType === 'file' ? '文件' : 
                 dragContentType === 'directory' ? '目录' : '释放'}
              </div>
            </div>
          )}
          
          {(isVoiceRecording || isLongPressing) ? (
            // 语音录入模式UI
            <div className="audio-wave-container">
              {audioWaves.map((wave) => (
                <div 
                  key={wave.id}
                  className="audio-wave"
                />
              ))}
              <div className="text-white z-10 flex items-center justify-center">
                {renderMicIcon()}
              </div>
            </div>
          ) : isCapturing ? (
            // 截图模式UI
            <div className="w-full h-full flex items-center justify-center">
              {renderCropIcon()}
            </div>
          ) : notifications.length > 0 ? (
            // 通知模式UI - 显示通知内容
            renderNotificationContent()
          ) : (
            // 默认UI - 图片和文字
            <div className={`w-full h-full flex items-center relative bg-white ${isDragging ? 'justify-center' : snapSide === 'right' ? 'pl-[3px] justify-start ' : 'pr-[3px] justify-end'}`}>
              {!isHovered || isDragging ? (
                // 未悬停状态或拖动状态 - 只显示小图标
                <img
                  src="../../assets/images/icon_64x64.png"
                  alt="Aido"
                  className="object-contain rounded-full"
                  style={{width: '32px', height: '32px'}}
                  draggable={false}
                />
              ) : (
                // 悬停状态且非拖动状态 - 根据吸附方向动态渲染
                snapSide === 'right' ? (
                  // 吸附在右侧，内容靠左（圆形边缘侧）
                  <div className="flex items-center justify-start w-full h-full">
                    <img
                      src="../../assets/images/icon_64x64.png"
                      alt="Aido"
                      className="object-contain rounded-full"
                      style={{width: '32px', height: '32px'}}
                      draggable={false}
                    />
                  </div>
                ) : (
                  // 吸附在左侧，内容靠右（圆形边缘侧）
                  <div className="flex items-center justify-end w-full h-full">
                    <img
                      src="../../assets/images/icon_64x64.png"
                      alt="Aido"
                      className="object-contain rounded-full"
                      style={{width: '32px', height: '32px'}}
                      draggable={false}
                    />
                  </div>
                )
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
);

// 添加显示名称
FloatingBallUI.displayName = 'FloatingBallUI'; 