import React, { useEffect, useRef, useState, useCallback } from 'react';
import { floatingBallClient } from '../../services/api/floating-ball';
import { FloatingBallUI } from './FloatingBallUI';
import { AudioRecorder } from './audioRecorder';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';
import { changeLanguage } from '../../i18n';
/**
 * 悬浮球页面
 * 使用重构后的悬浮球UI组件
 */
const FloatingBallPage: React.FC = () => {
  // 状态
  const [isCapturing, setIsCapturing] = useState(false);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const [voiceVolume, setVoiceVolume] = useState(0);
  const [snapSide, setSnapSide] = useState<'left' | 'right'>('right');
  
  // 拖拽内容状态
  const [dropDialog, setDropDialog] = useState<{
    open: boolean;
    type: 'text' | 'file' | 'image' | 'directory';
    content: any;
  }>({
    open: false,
    type: 'text',
    content: null
  });
  
  // 引用
  const ballRef = useRef<HTMLDivElement>(null);

  // 音频录制器
  const audioRecorder = useRef<AudioRecorder>(new AudioRecorder());
  
  // 组件初始化时监听消息
  useEffect(() => {
    // 订阅语音录制状态变化
    const unsubscribeVoice = floatingBallClient.onVoiceRecordingChange?.(
      (recording, volume) => {
        console.log('语音录制状态变化', { recording, volume });
        setIsVoiceRecording(recording);
        if (volume !== undefined) {
          setVoiceVolume(volume);
        }
      }
    );
    
    // 订阅截图模式状态变化
    const unsubscribeCapture = floatingBallClient.onScreenCaptureChange?.(
      (capturing) => {
        setIsCapturing(capturing);
      }
    );
    
    // 订阅吸附侧边变化
    const unsubscribeSnapSide = floatingBallClient.onSnapSideChange?.(
      (side) => {
        console.log('吸附侧边变化', side);
        setSnapSide(side);
      }
    );
    
    // 初始时设置鼠标事件穿透
    if (floatingBallClient.setIgnoreMouseEvents) {
      floatingBallClient.setIgnoreMouseEvents(true);
    }
    
    // 清理函数
    return () => {
      // 取消所有订阅
      if (unsubscribeVoice) unsubscribeVoice();
      if (unsubscribeCapture) unsubscribeCapture();
      if (unsubscribeSnapSide) unsubscribeSnapSide();
    };
  }, []);

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = (language: string) => {
      console.log(`悬浮球窗口收到语言变化事件: ${language}`);
      changeLanguage(language);
    };

    // 监听来自主进程的语言变化事件
    const removeListener = window.electron.system.onLanguageChanged(handleLanguageChange);

    return () => {
      removeListener();
    };
  }, []);
  
  // 处理鼠标悬停在悬浮球上
  const handleMouseEnter = useCallback(() => {
    console.log('鼠标进入悬浮球区域');
    // 禁用鼠标事件穿透，使悬浮球可以接收鼠标事件
    if (floatingBallClient.setIgnoreMouseEvents) {
      floatingBallClient.setIgnoreMouseEvents(false);
    }
  }, []);
  
  // 处理鼠标离开悬浮球
  const handleMouseLeave = useCallback(() => {
    console.log('鼠标离开悬浮球区域');
    // 重新启用鼠标事件穿透
    if (floatingBallClient.setIgnoreMouseEvents) {
      floatingBallClient.setIgnoreMouseEvents(true);
    }
  }, []);
  
  // 处理拖拽进入悬浮球
  const handleDragEnter = useCallback(() => {
    console.log('拖拽进入悬浮球区域');
    // 禁用鼠标事件穿透，确保能接收到拖放事件
    if (floatingBallClient.setIgnoreMouseEvents) {
      floatingBallClient.setIgnoreMouseEvents(false);
    }
  }, []);

  // 处理拖拽离开悬浮球
  const handleDragLeave = useCallback(() => {
    console.log('拖拽离开悬浮球区域');
    // 重新启用鼠标事件穿透
    if (floatingBallClient.setIgnoreMouseEvents) {
      floatingBallClient.setIgnoreMouseEvents(true);
    }
  }, []);

  // 处理拖拽完成（释放）
  const handleDragDrop = useCallback(() => {
    console.log('拖拽释放在悬浮球上');
    // 处理完拖拽事件后，延迟一段时间再恢复鼠标穿透
    // 这样确保所有相关事件都被正确处理
    setTimeout(() => {
      if (floatingBallClient.setIgnoreMouseEvents) {
        floatingBallClient.setIgnoreMouseEvents(true);
      }
    }, 300);
  }, []);
  
  // 单击处理
  const handleSingleClick = useCallback(() => {
    console.log('浮动球: 单击事件');
    floatingBallClient.click();
  }, []);
  
  // 双击处理 - 直接启动多屏截图
  const handleDoubleClick = useCallback(async () => {
    const startTime = performance.now();
    console.log(`[性能] 浮动球双击事件开始: ${startTime.toFixed(2)}ms`);

    if (!isCapturing) {
      try {
        // 检查是否为多显示器环境
        const displayCheckStart = performance.now();
        const displays = await floatingBallClient.getDisplays();
        const displayCheckEnd = performance.now();
        console.log(`[性能] 显示器检测耗时: ${(displayCheckEnd - displayCheckStart).toFixed(2)}ms`);

        if (displays.length > 1) {
          // 多显示器环境，直接启动所有屏幕截图
          console.log(`[性能] 检测到${displays.length}个显示器，启动多屏截图`);
          const captureStart = performance.now();
          floatingBallClient.startMultiScreenCapture();
          console.log(`[性能] 多屏截图启动调用耗时: ${(performance.now() - captureStart).toFixed(2)}ms`);
        } else {
          // 单显示器环境，使用原有逻辑
          console.log('[性能] 单显示器，启动传统截图模式');
          const captureStart = performance.now();
          floatingBallClient.doubleClick();
          console.log(`[性能] 传统截图启动调用耗时: ${(performance.now() - captureStart).toFixed(2)}ms`);
        }

        const totalTime = performance.now() - startTime;
        console.log(`[性能] 浮动球双击处理总耗时: ${totalTime.toFixed(2)}ms`);
      } catch (error) {
        console.error('检测显示器失败，使用传统截图模式:', error);
        floatingBallClient.doubleClick();
      }
    } else {
      // 如果已经在截图模式，则取消
      floatingBallClient.cancelCapture?.();
      console.log('浮动球: 取消截图模式');
    }
  }, [isCapturing]);
  
  // 长按处理
  const handleLongPress = useCallback(() => {
    if (!isVoiceRecording) {
      // 开始录音
      console.log('浮动球: 开始录音');
      audioRecorder.current.start();
    } else {
      // 结束录音
      console.log('浮动球: 结束录音');
      audioRecorder.current.stop();
    }
  }, [isVoiceRecording]);
  
  // 拖拽开始处理
  const handleDragStart = useCallback((e: React.MouseEvent) => {
    console.log('浮动球: 拖拽开始', { x: e.screenX, y: e.screenY });
    floatingBallClient.dragStart({ x: e.screenX, y: e.screenY });
  }, []);
  
  // 拖拽过程处理
  const handleDrag = useCallback((e: MouseEvent) => {
    floatingBallClient.dragMove({ x: e.screenX, y: e.screenY });
  }, []);
  
  // 拖拽结束处理
  const handleDragEnd = useCallback(() => {
    console.log('浮动球: 拖拽结束');
    floatingBallClient.dragEnd?.();
  }, []);
  
  // 屏幕镜像模式自动结束处理
  const handleCaptureEnd = useCallback(() => {
    console.log('浮动球: 屏幕镜像模式自动结束');
    if (isCapturing) {
      // 确保调用cancelCapture来通知主进程屏幕镜像已取消
      if (floatingBallClient.cancelCapture) {
        console.log('浮动球: 调用API取消屏幕镜像模式');
        floatingBallClient.cancelCapture();
      }
      setIsCapturing(false);
    }
  }, [isCapturing]);
  
  // 处理外部内容拖放
  const handleExternalDrop = useCallback((content: {
    type: 'text' | 'file' | 'image' | 'directory';
    data: any;
  }) => {
    console.log('收到外部拖放内容:', content);
    
    // 处理拖放完成事件
    handleDragDrop();
    
    // 打开对话框询问用户
    setDropDialog({
      open: true,
      type: content.type,
      content: content.data
    });
  }, [handleDragDrop]);
  
  // 处理对话框关闭
  const handleCloseDialog = useCallback(() => {
    setDropDialog(prev => ({ ...prev, open: false }));
  }, []);
  
  // 处理不同的操作
  const handleAction = useCallback((action: string) => {
    const { type, content } = dropDialog;
    
    // 这里根据不同的内容类型和操作执行不同的逻辑
    console.log(`执行操作: ${action}，内容类型: ${type}`);
    
    // 示例操作实现
    switch (action) {
      case 'copy':
        // 复制到剪贴板
        if (type === 'text') {
          navigator.clipboard.writeText(content).then(() => {
            console.log('文本已复制到剪贴板');
          });
        }
        break;
        
      case 'search':
        // 搜索内容
        if (type === 'text') {
          // 打开搜索页面或调用搜索API
          window.open(`https://www.baidu.com/s?wd=${encodeURIComponent(content)}`, '_blank');
        }
        break;
        
      case 'analyze':
        // 分析内容
        // 这里可以实现分析逻辑或调用分析API
        console.log('分析内容:', content);
        break;
        
      case 'open':
        // 打开文件或目录
        if (type === 'file' || type === 'image') {
          // 调用文件打开API
          // 实际实现应该使用Electron API
          console.log('打开文件:', content);
        }
        break;
        
      default:
        console.log('未实现的操作:', action);
    }
    
    // 关闭对话框
    handleCloseDialog();
  }, [dropDialog, handleCloseDialog]);
  
  // 渲染对话框内容
  const renderDialogContent = () => {
    const { type, content } = dropDialog;
    
    // 根据不同类型显示不同内容
    switch (type) {
      case 'text':
        return (
          <>
            <DialogHeader>
              <DialogTitle>文本内容</DialogTitle>
              <DialogDescription>
                您希望如何处理以下文本？
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-40 overflow-auto p-4 bg-gray-100 rounded-md text-sm">
              {typeof content === 'string' ? content : JSON.stringify(content)}
            </div>
            <DialogFooter className="mt-4 flex justify-end space-x-2">
              <Button variant="outline" onClick={handleCloseDialog}>取消</Button>
              <Button onClick={() => handleAction('copy')}>复制</Button>
              <Button onClick={() => handleAction('search')}>搜索</Button>
              <Button onClick={() => handleAction('analyze')}>分析</Button>
            </DialogFooter>
          </>
        );
        
      case 'file':
      case 'directory':
        // 文件/目录显示
        return (
          <>
            <DialogHeader>
              <DialogTitle>文件/目录</DialogTitle>
              <DialogDescription>
                您希望如何处理这些文件或目录？
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-40 overflow-auto p-4 bg-gray-100 rounded-md text-sm">
              <ul>
                {Array.isArray(content) ? 
                  content.map((item: any, index: number) => (
                    <li key={index} className="mb-1">
                      {item.name || item.path || '未命名'} 
                      {item.isDirectory ? ' (目录)' : ''}
                    </li>
                  )) : 
                  <li>{JSON.stringify(content)}</li>
                }
              </ul>
            </div>
            <DialogFooter className="mt-4 flex justify-end space-x-2">
              <Button variant="outline" onClick={handleCloseDialog}>取消</Button>
              <Button onClick={() => handleAction('open')}>打开</Button>
              <Button onClick={() => handleAction('analyze')}>分析</Button>
            </DialogFooter>
          </>
        );
        
      case 'image':
        // 图片显示
        return (
          <>
            <DialogHeader>
              <DialogTitle>图片文件</DialogTitle>
              <DialogDescription>
                您希望如何处理这些图片？
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-40 overflow-auto p-4 bg-gray-100 rounded-md text-sm">
              <ul>
                {Array.isArray(content) ? 
                  content.map((file: File, index: number) => (
                    <li key={index} className="mb-1">
                      {file.name} ({Math.round(file.size / 1024)} KB)
                    </li>
                  )) : 
                  <li>{JSON.stringify(content)}</li>
                }
              </ul>
            </div>
            <DialogFooter className="mt-4 flex justify-end space-x-2">
              <Button variant="outline" onClick={handleCloseDialog}>取消</Button>
              <Button onClick={() => handleAction('open')}>打开</Button>
              <Button onClick={() => handleAction('analyze')}>分析</Button>
            </DialogFooter>
          </>
        );
        
      default:
        return (
          <>
            <DialogHeader>
              <DialogTitle>未知内容</DialogTitle>
              <DialogDescription>
                无法处理此类型的内容
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button onClick={handleCloseDialog}>关闭</Button>
            </DialogFooter>
          </>
        );
    }
  };
  
  return (
    <div className="w-screen h-screen overflow-hidden">
      <FloatingBallUI
        ref={ballRef}
        isCapturing={isCapturing}
        isVoiceRecording={isVoiceRecording}
        voiceVolume={voiceVolume}
        snapSide={snapSide}
        onSingleClick={handleSingleClick}
        onDoubleClick={handleDoubleClick}
        onLongPress={handleLongPress}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        onCaptureEnd={handleCaptureEnd}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onExternalDrop={handleExternalDrop}
        onExternalDragEnter={handleDragEnter}
        onExternalDragLeave={handleDragLeave}
      />
      
      {/* 拖放内容处理对话框 */}
      <Dialog 
        open={dropDialog.open} 
        onOpenChange={(open) => setDropDialog(prev => ({ ...prev, open }))}
      >
        <DialogContent>
          {renderDialogContent()}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FloatingBallPage; 