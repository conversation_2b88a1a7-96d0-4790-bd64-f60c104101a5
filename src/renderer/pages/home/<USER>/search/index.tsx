import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ResultItem } from '../../../../types/app';
import { getBuiltinTools, getAppItem, getMcpTools, getAiResultItem, ComposingContext } from '../../index';
import PinyinMatch from 'pinyin-match';
import {
  classifyInput,
  InputType,
  getColorFormats,
  parseMathExpression,
  MathResult,
  getIpLocation,
  IpLocationResult
} from '../../../../utils/inputUtils';
import { HotkeyButton } from '../../../../components/ui/hotkey-button';
import { FilePathContext } from '../../index';
import { appIconListener } from '../../../../services/app-icon-listener';
import { Avatar } from 'antd';
import { appManagerClient } from '../../../../services/api/app-manager';
import { useAppModeStore } from '../../../../stores/appModeStore';
import { AppMode } from '../../../../types/app';

/**
 * 搜索页面组件的Props接口
 */
interface SearchPageProps {
  query?: string;
  onRouteChange?: (route: string, searchQuery?: string) => void;
}

/**
 * 搜索页面组件
 * 包含应用搜索、内置工具、MCP技能搜索
 * 以及颜色识别、数学计算、IP地址查询等特殊输入处理
 */
const SearchPage: React.FC<SearchPageProps> = ({ 
  query = '', 
  onRouteChange 
}) => {
  const { t } = useTranslation();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [filteredItems, setFilteredItems] = useState<ResultItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { isComposing } = useContext(ComposingContext);
  const [inputType, setInputType] = useState<InputType>('其他');
  const [colorData, setColorData] = useState<any>(null);
  const [mathData, setMathData] = useState<MathResult | null>(null);
  const [ipData, setIpData] = useState<IpLocationResult | null>(null);
  const { droppedFilePath } = useContext(FilePathContext);
  const [allApps, setAllApps] = useState<any[]>([]);
  const { setAppMode } = useAppModeStore();

  // 设置信息状态
  const [settings, setSettings] = useState<any>(null);

  // 获取应用列表
  useEffect(() => {
    const getAllApps = async () => {
      try {
        const appsList = await appManagerClient.getAppList();
        setAllApps(appsList);
      } catch (error) {
        console.error('获取应用列表失败:', error);
        setAllApps([]);
      }
    };
    getAllApps();
  }, []);

  // AI对话启动处理
  const handleAiChatStart = useCallback((searchQuery: string) => {
    console.log('启动AI对话:', searchQuery);
    setAppMode(AppMode.AI_CHAT);
    if (onRouteChange) {
      onRouteChange('/ai-chat', searchQuery);
    }
  }, [onRouteChange, setAppMode]);

  // 文件搜索启动处理
  const handleFileSearchStart = useCallback((searchQuery: string) => {
    console.log('启动文件搜索:', searchQuery);
    setAppMode(AppMode.FILE_SEARCH);
    if (onRouteChange) {
      onRouteChange('/file-search', searchQuery);
    }
  }, [onRouteChange, setAppMode]);

  // 翻译启动处理
  const handleTranslateStart = useCallback(() => {
    console.log('启动翻译功能');
    setAppMode(AppMode.TRANSLATOR);
    if (onRouteChange) {
      onRouteChange('/translate');
    }
  }, [onRouteChange, setAppMode]);

  // 获取文件扩展名的辅助函数
  const getFileExtensions = useCallback((filePaths: string): string[] => {
    if (!filePaths) return [];

    const paths = filePaths.split(',').filter(path => path.trim());
    const extensions: string[] = [];

    paths.forEach(path => {
      const fileName = path.split(/[/\\]/).pop() || '';
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex > 0) { // 确保不是隐藏文件（以.开头）
        const ext = fileName.substring(lastDotIndex).toLowerCase();
        if (!extensions.includes(ext)) {
          extensions.push(ext);
        }
      }
    });

    return extensions;
  }, []);

  // 执行选中的项目
  const handleExecuteItem = useCallback(async (index: number) => {
    const item = filteredItems[index];
    if (!item) return;
    
    // 直接执行项目的 execute 回调
    if (item.execute) {
      try {
        await item.execute(query);
      } catch (error) {
        console.error(`执行 ${item.name} 失败:`, error);
      }
    } else {
      console.warn(`项目 ${item.name} 没有提供执行函数`);
    }
  }, [filteredItems, query]);

  // 保存图标更新监听器的取消函数
  const iconListenersRef = useRef<Map<string, () => void>>(new Map());

  // 为应用项添加器图标更新监听
  const setupIconListeners = useCallback((items: ResultItem[]) => {
    // 清除旧的监听器
    iconListenersRef.current.forEach(cancelListener => cancelListener());
    iconListenersRef.current.clear();
    
    // 为每个应用项添加监听器
    items.forEach(item => {
      if ((item as any).type === 'app' && item.id) {
        const cancelListener = appIconListener.addListener(item.id, (iconPath) => {
          console.log(`应用 ${item.name} 图标已更新:`, iconPath);
          // 更新应用图标
          setFilteredItems(prev => 
            prev.map(prevItem => {
              if (prevItem.id === item.id) {
                // 更新图标路径
                const updatedItem = { ...prevItem, icon: iconPath };
                
                // 重新创建renderIcon函数，确保它使用新的图标路径
                if ((updatedItem as any).type === 'app') {
                  updatedItem.renderIcon = () => (
                    <Avatar 
                      src={iconPath} 
                      className="w-8 h-8 p-0.5 mr-2 rounded-[8px]" 
                    />
                  );
                }
                
                return updatedItem;
              }
              return prevItem;
            })
          );
        });
        
        iconListenersRef.current.set(item.id, cancelListener);
      }
    });
  }, []);

  // 搜索所有类型的项目（应用程序、内置工具、MCP工具）
  const searchAllItems = useCallback(async () => {
    setLoading(true);
    try {
      const allResults: ResultItem[] = [];

      // 获取当前拖入文件的扩展名
      const droppedFileExtensions = getFileExtensions(droppedFilePath || '');
      const hasQuery = query.trim().length > 0;

      // 1. 搜索应用程序（只有在有查询内容时才搜索）
      const descMap = new Map();
      const appOptions = hasQuery ? allApps
          .filter((plugin) => {
            // 使用 name 或 _name 作为应用名称
            const appName = plugin._name || plugin.name;
            if (!descMap.get(appName)) { // 过滤重复
              descMap.set(appName, true);
              let has = false;

              // 检查关键词匹配 - 确保 keyWords 存在
              const keyWords = plugin.keyWords || [];
              keyWords.some((keyWord: string) => {
                const match = PinyinMatch.match(keyWord, query);
                if (match) {
                  has = true;
                  plugin.name = appName;
                  plugin.match = match;
                  return true;
                }
                return false;
              });
              return has;
            } else {
              return false;
            }
          }) : [];

      const appItems = appOptions.map((plugin) => {
            // 使用工厂函数创建带 execute 回调的应用项
            const appItem = getAppItem(plugin);
            (appItem as any).type = 'app';
            (appItem as any).match = plugin.match;
            return appItem;
          });

      allResults.push(...appItems);

      // 2. 搜索内置工具（只有在有查询内容时才搜索）
      const builtinTools = getBuiltinTools(handleAiChatStart, handleFileSearchStart, undefined, handleTranslateStart);
      const matchedBuiltinTools = hasQuery ? builtinTools.filter(tool => {
        const toolName = tool.name.toLowerCase();
        const queryLower = query.toLowerCase();
        const hasMatch = toolName.includes(queryLower) ||
                        PinyinMatch.match(tool.name, query);
        if (hasMatch) {
          (tool as any).type = 'builtin';
          (tool as any).match = toolName.indexOf(queryLower);
        }
        return hasMatch;
      }) : [];
      
      allResults.push(...matchedBuiltinTools);

      // 3. 搜索MCP技能（从缓存中）
      try {
        const mcpTools = getMcpTools(); // 现在是同步调用，从缓存获取

        const matchedMcpTools = mcpTools.filter(tool => {
          const queryLower = query.toLowerCase();
          const hasQuery = query.trim().length > 0;

          // 搜索多个字段：中文描述、英文描述、中文名、英文名等
          const searchFields = [
            tool.name, // 这里已经是处理过的显示名称
            (tool as any).c_name,
            (tool as any).fullName,
            (tool as any).originalName // 如果有原始名称字段
          ].filter(Boolean); // 过滤掉null/undefined值

          let hasMatch = false;
          let matchIndex = 9999;

          // 只有在有查询内容时才进行文本匹配
          if (hasQuery) {
            for (const field of searchFields) {
              if (field) {
                const fieldLower = field.toLowerCase();
                // 检查完全匹配、包含匹配和拼音匹配
                if (fieldLower.includes(queryLower)) {
                  hasMatch = true;
                  matchIndex = Math.min(matchIndex, fieldLower.indexOf(queryLower));
                } else if (PinyinMatch.match(field, query)) {
                  hasMatch = true;
                  const pinyinMatch = PinyinMatch.match(field, query);
                  if (Array.isArray(pinyinMatch)) {
                    matchIndex = Math.min(matchIndex, pinyinMatch[0]);
                  } else if (typeof pinyinMatch === 'number') {
                    matchIndex = Math.min(matchIndex, pinyinMatch);
                  }
                }
              }
            }
          }

          // 如果有拖入文件，检查工具是否支持这些文件扩展名
          let hasFileExtensionMatch = false;
          if (droppedFileExtensions.length > 0) {
            const toolSupportedExtensions = (tool as any).supportedExtensions;
            if (toolSupportedExtensions && Array.isArray(toolSupportedExtensions)) {
              // 检查是否有任何拖入文件的扩展名被工具支持
              hasFileExtensionMatch = droppedFileExtensions.some(ext =>
                toolSupportedExtensions.some((supportedExt: string) =>
                  supportedExt.toLowerCase() === ext.toLowerCase()
                )
              );
            }
          }

          // 决定是否包含该工具：
          // 1. 如果有查询内容，需要文本匹配或文件扩展名匹配
          // 2. 如果没有查询内容，只需要文件扩展名匹配
          const shouldInclude = hasQuery ? (hasMatch || hasFileExtensionMatch) : hasFileExtensionMatch;

          if (shouldInclude) {
            (tool as any).type = 'mcp';
            (tool as any).match = hasFileExtensionMatch && !hasMatch ? -1 : matchIndex; // 文件扩展名匹配优先级更高
            (tool as any).hasFileExtensionMatch = hasFileExtensionMatch;
          }
          return shouldInclude;
        });

        allResults.push(...matchedMcpTools);
      } catch (error) {
        console.error('搜索MCP技能失败:', error);
      }

      // 排序规则：
      // 2. 文件扩展名匹配的MCP工具优先（当有拖入文件时）
      // 3. 类型优先级：应用程序 > 内置工具 > MCP工具
      // 4. 完全匹配（忽略大小写）优先
      // 5. 前缀匹配优先
      // 6. 其它匹配按照名称字典序
      const lowerQuery = query.toLowerCase();
      const hasDroppedFiles = getFileExtensions(droppedFilePath || '').length > 0;

      allResults.sort((a: ResultItem, b: ResultItem) => {
        const aName = (a.name || '').toLowerCase();
        const bName = (b.name || '').toLowerCase();

        // 当有拖入文件时，文件扩展名匹配的MCP工具优先
        if (hasDroppedFiles) {
          const aHasFileMatch = (a as any).hasFileExtensionMatch;
          const bHasFileMatch = (b as any).hasFileExtensionMatch;
          if (aHasFileMatch && !bHasFileMatch) return -1;
          if (bHasFileMatch && !aHasFileMatch) return 1;
        }

        // 类型优先级
        const typeOrder: { [key: string]: number } = { app: 1, builtin: 2, mcp: 3 };
        const aTypeOrder = typeOrder[(a as any).type] || 4;
        const bTypeOrder = typeOrder[(b as any).type] || 4;
        if (aTypeOrder !== bTypeOrder) return aTypeOrder - bTypeOrder;

        // 完全匹配优先
        const aExact = aName === lowerQuery;
        const bExact = bName === lowerQuery;
        if (aExact && !bExact) return -1;
        if (bExact && !aExact) return 1;

        // 前缀匹配优先
        const aStarts = aName.startsWith(lowerQuery);
        const bStarts = bName.startsWith(lowerQuery);
        if (aStarts && !bStarts) return -1;
        if (bStarts && !aStarts) return 1;

        // 如果查询是纯 ASCII（通常为拼音），根据匹配位置和名称长度进一步排序
        const asciiQuery = /^[\x20-\x7F]+$/.test(lowerQuery);
        if (asciiQuery) {
          const aIndex = typeof (a as any).match === 'number' ? (a as any).match : Array.isArray((a as any).match) ? (a as any).match[0] : 9999;
          const bIndex = typeof (b as any).match === 'number' ? (b as any).match : Array.isArray((b as any).match) ? (b as any).match[0] : 9999;

          if (aIndex !== bIndex) return aIndex - bIndex;

          // 相同起始位置时，名称越短优先
          if (aName.length !== bName.length) return aName.length - bName.length;
        }

        return aName.localeCompare(bName, 'zh-Hans-CN');
      });

      // 如果有查询内容，则添加 AI 对话项
      if (hasQuery) {
        allResults.push(getAiResultItem(handleAiChatStart));
      }

      setFilteredItems(allResults);
      
      // 加载已保存的快捷键
      try {
        // 加载设置信息
        let currentSettings = null;
        if (window.electron?.settings) {
          currentSettings = await window.electron.settings.get();
          setSettings(currentSettings);
        }

        // 加载应用快捷键
        if (window.electron?.shortcut) {
          const savedShortcuts = await window.electron.shortcut.getAppShortcuts();

          if (savedShortcuts.length > 0) {
            setFilteredItems(prev =>
              prev.map(item => {
                const savedShortcut = savedShortcuts.find(s => s.id === item.id);
                return savedShortcut
                  ? {
                      ...item,
                      shortcut: savedShortcut.shortcut
                    }
                  : item;
              })
            );
          }

          // 加载内置功能快捷键
          const savedBuiltinShortcuts = await window.electron.shortcut.getBuiltinShortcuts();

          if (savedBuiltinShortcuts.length > 0) {
            setFilteredItems(prev =>
              prev.map(item => {
                const savedShortcut = savedBuiltinShortcuts.find(s => s.id === item.id);
                return savedShortcut
                  ? {
                      ...item,
                      shortcut: savedShortcut.shortcut
                    }
                  : item;
              })
            );
          }

          // 为剪贴板功能设置快捷键（从设置中获取）
          if (currentSettings?.clipboard?.hotkey) {
            setFilteredItems(prev =>
              prev.map(item => {
                if (item.id === 'clipboard-history-mode') {
                  return {
                    ...item,
                    shortcut: currentSettings.clipboard.hotkey
                  };
                }
                return item;
              })
            );
          }
        }
        
      } catch (error) {
        console.error('加载已保存数据失败:', error);
      }
    } catch (error) {
      console.error('搜索项目出错:', error);
      setFilteredItems([]); // 出错时显示空列表
    } finally {
      setLoading(false);
    }
    setSelectedIndex(0);
  }, [query, allApps, handleAiChatStart, handleFileSearchStart, handleTranslateStart, droppedFilePath, getFileExtensions]);

  // 主搜索效果 - 只依赖查询和拖入文件
  useEffect(() => {
    // 只有在有查询内容或有拖入文件时才进行搜索
    if (query.trim() || (droppedFilePath && droppedFilePath.trim())) {
      searchAllItems();
    } else {
      // 清空结果
      setFilteredItems([]);
    }
  }, [query, droppedFilePath, searchAllItems]);

  // 特殊输入处理效果 - 独立处理
  useEffect(() => {
    // 处理特殊输入类型
    const type = classifyInput(query);
    setInputType(type);

    if (type === '颜色') {
      try {
        const colorInfo = getColorFormats(query);
        setColorData(colorInfo);
      } catch (error) {
        console.error('颜色解析错误:', error);
        setColorData(null);
      }
    } else {
      setColorData(null);
    }

    if (type === '数学计算') {
      parseMathExpression(query)
        .then(setMathData)
        .catch(error => {
          console.error('数学计算错误:', error);
          setMathData(null);
        });
    } else {
      setMathData(null);
    }

    if (type === 'IP地址') {
      getIpLocation(query)
        .then(result => {
          console.log('IP归属地查询成功:', result);
          setIpData(result);
        })
        .catch(error => {
          console.error('IP归属地查询错误:', error);
          setIpData(null);
        });
    } else {
      setIpData(null);
    }
  }, [query]);

  // 图标监听器设置效果 - 独立处理，避免循环依赖
  useEffect(() => {
    if (filteredItems.length > 0) {
      setupIconListeners(filteredItems);
    }
    
    // 清理函数
    return () => {
      iconListenersRef.current.forEach(cancelListener => cancelListener());
      iconListenersRef.current.clear();
    };
  }, [filteredItems.length]); // 只依赖长度，避免循环依赖

  // 处理键盘导航和SearchBar的自定义事件
  useEffect(() => {
    // 处理键盘事件（只处理导航，不处理回车）
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => (prev < filteredItems.length - 1 ? prev + 1 : prev));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : prev));
      }
      // 移除回车处理，只通过自定义事件处理，避免重复执行
    };

    // 处理SearchBar的自定义事件
    const handleSearchBarEnter = (e: CustomEvent) => {
      const { query: eventQuery, preventDefault } = e.detail;

      // 如果有匹配的工具，阻止SearchBar启动AI对话
      if (filteredItems.length > 0) {
        console.log('🔧 SearchPage通过自定义事件处理回车：执行工具', filteredItems[selectedIndex]?.name);
        preventDefault();
        handleExecuteItem(selectedIndex);
      } else if (
        eventQuery.trim() &&
        classifyInput(eventQuery) === '其他' &&
        handleAiChatStart
      ) {
        // 如果没有工具匹配但满足AI对话条件，由SearchPage启动AI对话
        console.log('🤖 SearchPage通过自定义事件处理回车：启动AI对话');
        preventDefault();
        handleAiChatStart(eventQuery);
      }
      // 如果都不满足，让SearchBar处理（不调用preventDefault）
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('searchbar-enter', handleSearchBarEnter as EventListener);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('searchbar-enter', handleSearchBarEnter as EventListener);
    };
  }, [
    filteredItems.length,
    selectedIndex,
    isComposing,
    query,
    handleAiChatStart,
    handleExecuteItem
  ]);

  // 处理快捷键更新
  const handleShortcutChange = async (itemId: string, newShortcut: string) => {
    try {
      // 检查快捷键API是否可用
      if (!window.electron?.shortcut) {
        console.error('快捷键API不可用');
        return;
      }

      // 找到对应的应用项
      const item = filteredItems.find(item => item.id === itemId);
      if (!item) {
        console.error('应用不存在');
        return;
      }

      // 检查快捷键冲突（包括UI中显示的所有快捷键）
      const existingShortcut = filteredItems.find(item =>
        item.id !== itemId && item.shortcut === newShortcut
      );
      if (existingShortcut) {
        console.error('快捷键与其他功能冲突:', existingShortcut.name);
        // 可以在这里显示用户友好的错误提示
        return false;
      }

      // 检查快捷键是否可用（后端会进行更全面的冲突检查）
      if (window.electron?.shortcut) {
        const isAvailable = await window.electron.shortcut.checkShortcutAvailable(newShortcut);
        if (!isAvailable) {
          console.error('快捷键已被系统或其他软件占用:', newShortcut);
          return false;
        }
      }

      let success = false;

      // 特殊处理剪贴板功能 - 更新设置而不是注册新的快捷键
      if (itemId === 'clipboard-history-mode') {
        if (window.electron?.settings) {
          success = await window.electron.settings.update({
            clipboard: { hotkey: newShortcut }
          });
        }
      } else {
        // 判断是内置功能还是应用
        const isBuiltinFunction = ['ai-chat-mode', 'file-search-mode', 'translate-tool', 'screenshot-tool'].includes(itemId);

        if (isBuiltinFunction) {
          // 注册内置功能快捷键
          success = await window.electron.shortcut.registerBuiltinShortcut(
            itemId,
            newShortcut,
            item.name
          );
        } else {
          // 注册应用快捷键（后端会自动转换格式）
          success = await window.electron.shortcut.registerAppShortcut(
            itemId,
            newShortcut,
            item.path || '',
            item.name
          );
        }
      }

      if (success) {
        // 更新UI中的快捷键显示（使用原始格式）
        setFilteredItems(prev => 
          prev.map(prevItem => 
            prevItem.id === itemId 
              ? { ...prevItem, shortcut: newShortcut }
              : prevItem
          )
        );
        console.log('快捷键设置成功:', itemId, newShortcut);
        return true;
      } else {
        console.error('快捷键注册失败');
        return false;
      }
    } catch (error) {
      console.error('设置快捷键出错:', error);
      return false;
    }
  };

  // 检查快捷键冲突
  const checkShortcutConflict = (shortcut: string, currentItemId: string): boolean => {
    const existingItem = filteredItems.find(item => 
      item.id !== currentItemId && item.shortcut === shortcut
    );
    return !!existingItem;
  };

  const handleItemClick = (index: number) => {
    setSelectedIndex(index);
    handleExecuteItem(index);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      console.log('复制成功');
    }).catch(err => {
      console.error('复制失败:', err);
    });
  };

  // 渲染颜色展示
  const renderColorDisplay = () => {
    if (!colorData) return null;

    const [r, g, b] = colorData.rgb;
    const bgColor = `rgb(${r}, ${g}, ${b})`;

    // 将RGB转换为16进制
    const toHex = (n: number) => n.toString(16).padStart(2, '0');
    const hexColor = `#${toHex(r)}${toHex(g)}${toHex(b)}`;

    return (
      <div className="mx-4 my-2">
        <div className="relative rounded-xl bg-white dark:bg-gray-900 border border-gray-200/60 dark:border-gray-700/60 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="p-4">
            <div className="flex items-center gap-4">
              {/* 颜色预览 */}
              <div
                className="w-12 h-12 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer transition-transform hover:scale-105 flex-shrink-0"
                style={{ backgroundColor: bgColor }}
                onClick={() => copyToClipboard(query)}
              />

              {/* 颜色格式 - 紧凑网格布局 */}
              <div className="flex-1 grid grid-cols-2 gap-2 text-xs">
                {/* 16进制颜色 */}
                <div
                  className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border"
                  style={{ borderColor: bgColor }}
                  onClick={() => copyToClipboard(hexColor)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded">
                        HEX
                      </span>
                      <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                        {hexColor}
                      </span>
                    </div>
                  </div>
                  <div className="flex-shrink-0 ml-2">
                    <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                      <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* 其他颜色格式 */}
                {Object.entries(colorData.formats).map(([key, value]) => {
                  const [label, colorValue] = (value as string).split(' | ');
                  return (
                    <div
                      key={key}
                      className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border"
                      style={{ borderColor: bgColor }}
                      onClick={() => copyToClipboard(colorValue)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded">
                            {label}
                          </span>
                          <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                            {colorValue}
                          </span>
                        </div>
                      </div>
                      <div className="flex-shrink-0 ml-2">
                        <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                          <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          {/* 微妙的顶部高光 */}
          <div className="absolute top-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-600 to-transparent"></div>
        </div>
      </div>
    );
  };

  // 渲染数学计算展示
  const renderMathDisplay = () => {
    if (!mathData) return null;

    const isSpecial = mathData.isSpecial;

    return (
      <div className="mx-4 my-2">
        <div className="relative rounded-xl bg-white dark:bg-gray-900 border border-gray-200/60 dark:border-gray-700/60 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="p-4 space-y-3">
            {/* 主要结果 */}
            <div
              className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border border-gray-200 dark:border-gray-700"
              onClick={() => copyToClipboard(isSpecial ? mathData.resultText : mathData.result.toString())}
            >
              <div className="flex-1 min-w-0">
                <div className="text-sm font-mono text-gray-900 dark:text-gray-100">
                  {isSpecial ? mathData.resultText : `${mathData.expression} = ${mathData.result}`}
                </div>
              </div>
              <div className="flex-shrink-0 ml-2">
                <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                  <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* 数值结果（积分特殊处理） */}
            {isSpecial && mathData.specialType === 'integral' && mathData.result !== 0 && (
              <div
                className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border border-gray-200 dark:border-gray-700"
                onClick={() => copyToClipboard(mathData.result.toString())}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded">
                      数值
                    </span>
                    <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                      {mathData.result}
                    </span>
                  </div>
                </div>
                <div className="flex-shrink-0 ml-2">
                  <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                    <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </div>
            )}

            {/* 人民币大写（非特殊计算或积分有数值时显示） */}
            {(!isSpecial || (mathData.specialType === 'integral' && mathData.result !== 0)) && (
              <div
                className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border border-gray-200 dark:border-gray-700"
                onClick={() => copyToClipboard(mathData.rmbText)}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded">
                      大写
                    </span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {mathData.rmbText}
                    </span>
                  </div>
                </div>
                <div className="flex-shrink-0 ml-2">
                  <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                    <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </div>
            )}
          </div>
          {/* 微妙的顶部高光 */}
          <div className="absolute top-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-600 to-transparent"></div>
        </div>
      </div>
    );
  };

  // 渲染IP地址归属地展示
  const renderIpDisplay = () => {
    if (!ipData) return null;

    // 构建归属地显示文本
    const buildLocationText = () => {
      const parts: string[] = [];
      if (ipData.detailInfo.country) parts.push(ipData.detailInfo.country);
      if (ipData.detailInfo.province) parts.push(ipData.detailInfo.province);
      if (ipData.detailInfo.city) parts.push(ipData.detailInfo.city);
      if (ipData.detailInfo.district) parts.push(ipData.detailInfo.district);
      return parts.join(' ');
    };

    return (
      <div className="mx-4 my-2">
        <div className="relative rounded-xl bg-white dark:bg-gray-900 border border-gray-200/60 dark:border-gray-700/60 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="p-4 space-y-3">
            {/* 归属地信息 */}
            <div
              className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border border-gray-200 dark:border-gray-700"
              onClick={() => copyToClipboard(buildLocationText() || '未知位置')}
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded">
                    归属地
                  </span>
                  <span className="text-sm text-gray-900 dark:text-gray-100">
                    {buildLocationText() || '未知位置'}
                  </span>
                </div>
              </div>
              <div className="flex-shrink-0 ml-2">
                <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                  <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* 运营商信息 */}
            {ipData.detailInfo.isp && (
              <div
                className="group flex items-center justify-between px-3 py-2 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors border border-gray-200 dark:border-gray-700"
                onClick={() => copyToClipboard(ipData.detailInfo.isp || '')}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded">
                      运营商
                    </span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {ipData.detailInfo.isp}
                    </span>
                  </div>
                </div>
                <div className="flex-shrink-0 ml-2">
                  <div className="w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 flex items-center justify-center transition-colors">
                    <svg className="w-3 h-3 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </div>
            )}
          </div>
          {/* 微妙的顶部高光 */}
          <div className="absolute top-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-600 to-transparent"></div>
        </div>
      </div>
    );
  };

  return (
    // 有输入内容或有拖入文件时才显示整个区域
    (query.trim() || (droppedFilePath && droppedFilePath.trim())) ? (
      <div id="results-window" className="bg-background dark:bg-gray-800 dark:text-gray-100 h-full flex flex-col">
        {/* 顶部固定内容 */}
        {inputType === '颜色' && renderColorDisplay()}
        {inputType === '数学计算' && renderMathDisplay()}
        {inputType === 'IP地址' && renderIpDisplay()}

        {/* 可滚动的搜索结果区域 */}
        <div className="flex-1 overflow-y-auto">
          {filteredItems.map((item, index) => (
            <div 
              key={item.id} 
              className={`flex justify-between items-center px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${selectedIndex === index ? 'bg-gray-100 dark:bg-gray-700' : ''} w-full`}
              onClick={() => handleItemClick(index)}
            >
              <div className="flex items-center flex-1 min-w-0 mr-2">
                {item.renderIcon ? item.renderIcon() : <div className="h-6 w-6 mr-2 bg-gray-200 rounded flex-shrink-0" />}
                <div className="flex flex-col flex-1 min-w-0">
                  <div className="flex items-center gap-2 min-w-0">
                    <span className="truncate flex-1">{item.name}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-1 flex-shrink-0">
                {((item as any).type === 'app' || (item as any).type === 'builtin') && (
                  <div onClick={(e) => e.stopPropagation()}>
                    <HotkeyButton 
                      shortcutId={`search-result-${item.id}`}
                      value={item.shortcut || ''}
                      onChange={(newShortcut) => handleShortcutChange(item.id, newShortcut)}
                      simpleMode={true}
                      checkConflict={(shortcut) => checkShortcutConflict(shortcut, item.id)}
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    ) : null
  );
};

export default SearchPage;
