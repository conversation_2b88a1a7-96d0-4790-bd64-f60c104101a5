import React, { useState, useEffect, useRef, forwardRef, useCallback, useImperative<PERSON>andle, createContext, useContext } from 'react';
import { Input, InputRef, Tooltip, ConfigProvider } from 'antd';
import { Button } from '../../../components/ui/button';
import { appManagerClient } from '../../../services/api/app-manager';
import useDrag from '../../../utils/dragWindow';
import {
  Wrench,
  Globe,
  Brain,
  Mic,
  ArrowLeft,
  Pin,
  X,
  File,
  Files
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { windowManagerClient } from '../../../services/api/window-manager';
import { FilePathContext } from '../index';
import { AppMode, ResultItem } from '../../../types/app';
import { RouteType } from '../index';
import useToolStore from '../../../stores/toolStore';
import { clipboardManagerClient } from '../../../services/api/clipboard-manager';
import { useAppModeStore } from '../../../stores/appModeStore';
import { useAuthStore } from '../../../stores/authStore';

interface SearchBarProps {
  disabled?: boolean;
  onBackClick?: () => void;
  // 新增的受控组件props
  query?: string;
  onQueryChange?: (query: string) => void;
  onRouteChange?: (route: RouteType, searchQuery?: string) => void;
  currentRoute?: RouteType;
  onBackToHome?: () => void;
  onAiChatComplete?: () => void;
  // AI对话完成计数器，用于触发状态重置
  aiChatCompleteCounter?: number;
  // 输入法状态管理
  isComposing?: boolean;
  onComposingChange?: (isComposing: boolean) => void;
}

/**
 * 搜索栏组件
 * 处理搜索输入、技能按钮和内容展示
 */
const SearchBar = ({
  disabled = false,
  query: externalQuery,
  onQueryChange: externalOnQueryChange,
  onRouteChange: externalOnRouteChange,
  currentRoute: externalCurrentRoute,
  onBackToHome: externalOnBackToHome,
  onAiChatComplete: externalOnAiChatComplete,
  aiChatCompleteCounter = 0,
  isComposing: externalIsComposing,
  onComposingChange: externalOnComposingChange,
}: SearchBarProps) => {
  const { t } = useTranslation();
  const inputRef = useRef<InputRef>(null);
  
  // 使用全局appMode状态
  const { currentMode: appMode, setAppMode } = useAppModeStore();
  
  // 如果有外部路由状态，优先使用外部状态
  const currentRoute = externalCurrentRoute;
  const handleRouteChange = externalOnRouteChange;
  const handleBackToHome = externalOnBackToHome || (() => {
    focusAndReset();
    setMcpToolTitle(null);
    setMcpToolInfo(null);
    setDroppedFiles([]);
    setGlobalDroppedFilePath(null);
    setAppMode(AppMode.HOME);
  });
  
  // 使用 ref 追踪最新 appMode，供异步回调使用
  const appModeRef = useRef(appMode);
  
  // 输入法状态管理：优先使用外部状态，如果没有则使用内部状态
  const [internalIsComposing, setInternalIsComposing] = useState(false);
  const isComposingState = externalIsComposing !== undefined ? externalIsComposing : internalIsComposing;
  const setIsComposingState = externalOnComposingChange || setInternalIsComposing;

  // 使用外部传入的query或内部状态
  const [internalQuery, setInternalQuery] = useState('');
  // 在AI对话模式下或输入法输入过程中，优先使用内部状态
  const query = (appMode === AppMode.AI_CHAT || isComposingState) ? internalQuery : (externalQuery !== undefined ? externalQuery : internalQuery);
  const setQuery = externalOnQueryChange || setInternalQuery;

  // 同步外部query到内部状态（当不在输入法输入过程中时）
  useEffect(() => {
    if (!isComposingState && externalQuery !== undefined) {
      setInternalQuery(externalQuery);
    }
  }, [externalQuery, isComposingState]);
  const [searchBarDisabled, setSearchBarDisabled] = useState(disabled);
  const [mcpToolTitle, setMcpToolTitle] = useState<string | null>(null);
  const [mcpToolInfo, setMcpToolInfo] = useState<any>(null);
  const [droppedFiles, setDroppedFiles] = useState<string[]>([]);
  
  // 使用全局技能存储
  const { activeTools, toggleTool, setToolEnabled } = useToolStore();

  // 获取全局文件路径上下文
  const { droppedFilePath: globalDroppedFilePath, setDroppedFilePath: setGlobalDroppedFilePath } = useContext(FilePathContext);

  // 获取主题信息（使用与设置窗口相同的方式）
  const [currentTheme, setCurrentTheme] = useState<string>(() => {
    return localStorage.getItem("aido-theme") || 'system';
  });

  // 监听主题变化
  useEffect(() => {
    const handleStorageChange = () => {
      const newTheme = localStorage.getItem("aido-theme") || 'system';
      setCurrentTheme(newTheme);
    };

    // 监听localStorage变化（跨标签页）
    window.addEventListener('storage', handleStorageChange);

    // 监听DOM class变化（同页面内主题切换）
    const observer = new MutationObserver(() => {
      const newTheme = localStorage.getItem("aido-theme") || 'system';
      setCurrentTheme(newTheme);
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      observer.disconnect();
    };
  }, []);

  // 检测当前是否为暗色模式（使用与ai-chat相同的方式）
  const isDarkMode = document.documentElement.classList.contains('dark');

  // 调试日志
  useEffect(() => {
    console.log('🎨 SearchBar主题状态:', {
      currentTheme,
      isDarkMode,
      documentClass: document.documentElement.className,
      localStorage: localStorage.getItem("aido-theme")
    });
  }, [currentTheme, isDarkMode]);

  // 使用认证Hook
  // const { requireAuth } = useAuth();


  // 防止回车提交表单
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 使用KeyboardEvent对象的重复标志来判断是否是输入法触发的回车
    // 输入法选词的回车通常不会触发repeat，而用户手动按住回车会触发
    if (e.key === 'Enter') {
      // 如果是中文输入法触发的回车，阻止事件处理
      if (e.nativeEvent.isComposing) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }

      const value = e.currentTarget.value;
      
      // 在剪贴板历史模式下，回车选择第一个匹配的项目
      if (appMode === AppMode.CLIPBOARD_HISTORY) {
        // 这里可以添加选择第一个匹配项目的逻辑
        // 暂时不处理，让剪贴板历史组件自己处理
        return;
      }
      
      // 在 AI_CHAT 模式下，确保是真实的用户操作回车，而不是输入法选词触发的隐式回车
      if (appMode === AppMode.AI_CHAT) {
        if (value.trim()) {
          console.log('🚀 AI对话模式下继续对话:', value);
          setSearchBarDisabled(true);// 防止重复提交
          // 在AI对话模式下，直接启动新的对话，不需要路由切换
          setTimeout(() => {
            startAiConversationInCurrentMode(value);
          }, 10);
        }
      }
      // 在主页或搜索模式下，如果有查询内容且没有特殊处理，启动AI对话
      else if ((appMode === AppMode.HOME || appMode === AppMode.SEARCH) && value.trim()) {
        // 在主页模式下，SearchPage可能有匹配的工具需要处理
        // 我们需要给SearchPage一个机会来处理回车事件
        e.preventDefault(); // 阻止默认行为

        // 设置一个标志，表示SearchBar想要启动AI对话
        let shouldStartAiChat = true;

        // 创建一个自定义事件，让SearchPage有机会阻止AI对话
        const searchPageEvent = new CustomEvent('searchbar-enter', {
          detail: {
            query: value,
            preventDefault: () => { shouldStartAiChat = false; }
          }
        });

        // 分发事件给SearchPage
        window.dispatchEvent(searchPageEvent);

        // 给SearchPage一点时间来处理事件
        setTimeout(() => {
          if (shouldStartAiChat) {
            console.log('🚀 主页回车启动AI对话:', value);

            // 先切换到AI对话模式
            setAppMode(AppMode.AI_CHAT);
            setQuery(value); // 确保query被设置

            // 等待AI对话组件渲染完成后再启动对话
            setTimeout(() => {
              startAiConversation(value);
            }, 200);
          } else {
            console.log('🛑 SearchPage已处理回车事件，不启动AI对话');
          }
        }, 50); // 给SearchPage 50ms的时间来处理
      }
      // 在 SEARCH 模式下，如果没有查询内容，让事件继续传播给 SearchResults 组件处理
    }
  };

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (searchBarDisabled) return;
    const value = e.target.value;

    // 如果正在使用输入法输入，只更新内部显示状态，不触发外部更新
    if (isComposingState) {
      console.log('🎌 输入法输入中，只更新内部状态:', value);
      setInternalQuery(value);
      return;
    }

    // 输入法结束后或非输入法输入时，正常更新状态
    // 在AI对话模式下，只更新内部状态，不触发外部回调
    // 只有在按回车时才会通过startAiConversationInCurrentMode更新外部query
    if (appMode === AppMode.AI_CHAT) {
      setInternalQuery(value);
    } else {
      // 在其他模式下，正常更新外部query
      if (externalOnQueryChange) {
        externalOnQueryChange(value);
      } else {
        setQuery(value);
      }
    }
    
    // 根据查询内容自动切换模式
    console.log("queryChange", appMode)
    // 如果当前处于剪贴板历史模式，保持该模式不变
    if (appMode === AppMode.CLIPBOARD_HISTORY) {
      // 在剪贴板历史模式下，搜索内容会自动过滤剪贴板历史
      return;
    }
    // 如果处于AI或文件搜索模式，也不自动切换
    if (appMode !== AppMode.AI_CHAT && appMode !== AppMode.FILE_SEARCH) {
      if (value.length === 0) {
        setAppMode(AppMode.HOME);
      } else {
        setAppMode(AppMode.SEARCH);
      }
    }
  };

    // 输入法开始输入
    const handleCompositionStart = () => {
      console.log('🎌 输入法开始输入');
      // 在输入法开始时，确保内部状态与当前显示的内容一致
      if (externalQuery !== undefined) {
        setInternalQuery(externalQuery);
      }
      setIsComposingState(true);
    };
  
    // 输入法输入过程中
    const handleCompositionUpdate = () => {
      setIsComposingState(true);
    };
  
      // 输入法输入结束（选词完成）
  const handleCompositionEnd = (e: React.CompositionEvent<HTMLInputElement>) => {
    console.log('🎌 输入法结束输入');
    setIsComposingState(false);
    // 在输入法结束后，需要更新query状态并触发搜索逻辑
    const value = e.currentTarget.value;

    console.log("compositionEnd queryChange", appMode, value);

    // 更新query状态
    if (appMode === AppMode.AI_CHAT) {
      setInternalQuery(value);
    } else {
      // 在其他模式下，正常更新外部query
      if (externalOnQueryChange) {
        externalOnQueryChange(value);
      } else {
        setQuery(value);
      }
    }

    // 触发搜索逻辑
    // 如果当前处于剪贴板历史模式，保持该模式不变
    if (appMode === AppMode.CLIPBOARD_HISTORY) {
      // 在剪贴板历史模式下，搜索内容会自动过滤剪贴板历史
      return;
    }
    // 如果处于AI或文件搜索模式，也不自动切换
    if (appMode !== AppMode.AI_CHAT && appMode !== AppMode.FILE_SEARCH) {
      if (value.length === 0) {
        setAppMode(AppMode.HOME);
      } else {
        setAppMode(AppMode.SEARCH);
      }
    }
  };
  
  // 开始AI对话
  const handleStartAiChat = (initialQuery: string) => {
    console.log('🚀 handleStartAiChat被调用，查询:', initialQuery);
    
    // 直接获取最新的认证状态，而不是依赖requireAuth函数
    const { isLoggedIn, user, token } = useAuthStore.getState();
    // 检查登录状态
    if (!isLoggedIn || !user || !token) {
      console.log('❌ handleStartAiChat: 用户未登录，弹出登录框');
      useAuthStore.getState().openLoginDialog();
      return;
    }
    console.log('✅ handleStartAiChat: 用户已登录，继续执行');
    
    // 不要强制清理WebSocket连接，避免中断MCP工具的响应
    // AI对话组件会自己管理连接状态
    console.log('🔌 切换到AI对话模式，保持现有连接状态');
    
    setAppMode(AppMode.AI_CHAT);
    if (initialQuery) {
      setQuery(initialQuery);
      // 切换到AI对话模式后立即启动对话，不需要用户再次按回车
      setSearchBarDisabled(true);
      setTimeout(() => {
        startAiConversation(initialQuery);
      }, 200); // 给模式切换和连接建立更多时间
    }
  };

  // 开始文件搜索
  const startFileSearch = (initialQuery: string) => {
    setAppMode(AppMode.FILE_SEARCH);
    if (initialQuery) {
      setQuery(initialQuery);
    }
  };

  // 开始剪贴板历史
  const startClipboardHistory = () => {
    console.log('进入剪贴板历史模式');
    setAppMode(AppMode.CLIPBOARD_HISTORY);
    setQuery(''); // 清空查询
    // 移除输入框焦点，避免抢占键盘事件
    setTimeout(() => {
      inputRef.current?.blur();
    }, 0);
    
    // 初始化剪贴板数据
    if (!clipboardManagerClient) {
      console.warn('clipboardManagerClient 未导入');
    } else {
      clipboardManagerClient.initialize();
    }
  };

  // 开始翻译
  const startTranslator = () => {
    console.log('进入翻译模式');
    setAppMode(AppMode.TRANSLATOR);
    setQuery(''); // 清空查询
    // 移除输入框焦点，避免抢占键盘事件
    setTimeout(() => {
      inputRef.current?.blur();
    }, 0);
  };

  // 通用函数：聚焦并选择搜索框
  const focusAndReset = () => {
    setSearchBarDisabled(false);
    setQuery(''); // 恢复清空查询状态的代码
    focusSearchInput();
  };

  // AI对话完成回调：只重置输入框状态，不返回主页
  const onAiChatComplete = useCallback(() => {
    console.log('SearchBar onAiChatComplete 被调用');
    setSearchBarDisabled(false);
    setQuery(''); // 清空输入框
    // 在AI对话模式下不自动聚焦，让用户继续查看对话
    if (appMode !== AppMode.AI_CHAT) {
      focusSearchInput();
    }
  }, [appMode, setQuery]);

  // 单独的外部回调调用函数，避免循环调用
  const callExternalOnAiChatComplete = useCallback(() => {
    if (externalOnAiChatComplete) {
      externalOnAiChatComplete();
    }
  }, [externalOnAiChatComplete]);

  // 聚焦搜索框并全选函数
  const focusSearchInput = () => {
    // 剪贴板历史模式不自动聚焦
    if (appMode === AppMode.CLIPBOARD_HISTORY) return;

    requestAnimationFrame(() => {
      inputRef.current?.focus();
      if (inputRef.current?.input) {
        inputRef.current.input.select();
      }
    });
  };

  // 窗口显示时聚焦搜索框并全选，方便快速输入新内容
  useEffect(() => {
    getAllApps();
    // 从主进程获取当前钉住状态并同步到本地状态
    const syncPinState = async () => {
      try {
        const isPinned = await windowManagerClient.isPinned();
        setToolEnabled('pin', isPinned);
      } catch (error) {
        console.error('获取钉住状态失败:', error);
      }
    };
    syncPinState();
    
    const unsubscribe = windowManagerClient.onWindowShow(() => {
      // 延时检查当前模式，避免在剪贴板消息到达前过早聚焦
      setTimeout(() => {
        if (appModeRef.current !== AppMode.CLIPBOARD_HISTORY) {
          focusSearchInput();
        }
      }, 20);
    });
    
    // 监听主进程发送的显示剪贴板历史消息
    const unsubscribeClipboardHistory = window.electron.window.onShowClipboardHistory(() => {
      console.log('收到显示剪贴板历史消息');
      startClipboardHistory();
    });

    // 监听内置功能快捷键触发消息
    const handleSwitchToTranslator = () => {
      console.log('收到切换到翻译模式消息');
      startTranslator();
    };

    const handleSwitchToAiChat = () => {
      console.log('收到切换到AI对话模式消息');
      startAiConversation('');
    };

    const handleSwitchToFileSearch = () => {
      console.log('收到切换到文件搜索模式消息');
      startFileSearch('');
    };

    const handleTriggerScreenshot = () => {
      console.log('收到触发截图消息');
      // 调用截图功能
      if (window.electron?.floatingBall?.doubleClick) {
        window.electron.floatingBall.doubleClick();
      }
    };

    // 监听MCP技能触发的AI对话切换事件
    const handleMcpSwitchToAiChat = (event: CustomEvent) => {
      console.log('收到MCP技能触发的AI对话切换事件:', event.detail);
      // 设置MCP技能标题和信息
      setMcpToolTitle(event.detail.toolMessage || '');
      setMcpToolInfo(event.detail.toolInfo || null);
      // 切换到AI对话模式，并传入技能消息
      handleStartAiChat(event.detail.toolMessage || '');
    };

    window.addEventListener('mcp-switch-to-ai-chat', handleMcpSwitchToAiChat as EventListener);

    // 监听文件拖入事件
    const handleFilesDropped = (data: any) => {
      if (data && data.filesPath && Array.isArray(data.filesPath)) {
        setDroppedFiles(data.filesPath);
        // 同步更新全局状态
        const filePath = data.filesPath.join(',');
        setGlobalDroppedFilePath(filePath);

        // 关键：当有文件拖入时，切换到SEARCH模式以确保SearchResults组件被渲染
        if (appMode !== AppMode.SEARCH) {
          setAppMode(AppMode.SEARCH);
        }
      }
    };

    // 添加事件监听器
    const unsubscribeTranslator = window.electron.window.onSwitchToTranslator(handleSwitchToTranslator);
    const unsubscribeAiChat = window.electron.window.onSwitchToAiChat(handleSwitchToAiChat);
    const unsubscribeFileSearch = window.electron.window.onSwitchToFileSearch(handleSwitchToFileSearch);
    const unsubscribeScreenshot = window.electron.window.onTriggerScreenshot(handleTriggerScreenshot);
    const unsubscribeFilesDropped = window.electron.crossWindow.on('pet:files-dropped', handleFilesDropped);

    return () => {
      if (typeof unsubscribe === 'function') unsubscribe();
      if (typeof unsubscribeClipboardHistory === 'function') unsubscribeClipboardHistory();

      // 移除事件监听器
      if (typeof unsubscribeTranslator === 'function') unsubscribeTranslator();
      if (typeof unsubscribeAiChat === 'function') unsubscribeAiChat();
      if (typeof unsubscribeFileSearch === 'function') unsubscribeFileSearch();
      if (typeof unsubscribeScreenshot === 'function') unsubscribeScreenshot();
      if (typeof unsubscribeFilesDropped === 'function') unsubscribeFilesDropped();

      // 移除MCP技能事件监听器
      window.removeEventListener('mcp-switch-to-ai-chat', handleMcpSwitchToAiChat as EventListener);
    };
  }, []);

  // 监听模式变化，当离开AI对话模式时清除MCP技能标题
  useEffect(() => {
    if (appMode !== AppMode.AI_CHAT) {
      setMcpToolTitle(null);
      setMcpToolInfo(null);
    }
  }, [appMode]);

  // 监听AI对话完成计数器变化，触发状态重置
  useEffect(() => {
    if (aiChatCompleteCounter > 0) {
      console.log('AI对话完成计数器变化，触发状态重置:', aiChatCompleteCounter);
      // 只调用内部状态重置，不调用外部回调避免循环
      setSearchBarDisabled(false);
      // 在AI对话模式下，重置内部状态
      if (appMode === AppMode.AI_CHAT) {
        setInternalQuery(''); // 清空内部输入框状态
      } else {
        setQuery(''); // 清空外部状态
      }
      // 在AI对话模式下不自动聚焦，让用户继续查看对话
      if (appMode !== AppMode.AI_CHAT) {
        focusSearchInput();
      }
    }
  }, [aiChatCompleteCounter, setQuery, appMode]);

  // 监听钉住状态变化，同步到主进程
  useEffect(() => {
    const syncPinToMain = async () => {
      try {
        await windowManagerClient.setPinned(activeTools.pin);
      } catch (error) {
        console.error('设置钉住状态失败:', error);
      }
    };
    syncPinToMain();
  }, [activeTools.pin]);

  // 监听应用模式变化，确保输入框状态正确
  useEffect(() => {
    console.log('🎨 SearchBar应用模式变化:', appMode);

    // 当返回主页时，确保输入框不是disabled状态
    if (appMode === AppMode.HOME) {
      console.log('🏠 返回主页，重置输入框状态');
      setSearchBarDisabled(false);
    }
  }, [appMode]);

  // 监听query和appMode变化，动态调整窗口高度
  useEffect(() => {
    const needsFullHeight = appMode === AppMode.AI_CHAT ||
                            appMode === AppMode.FILE_SEARCH ||
                            appMode === AppMode.CLIPBOARD_HISTORY ||
                            appMode === AppMode.TRANSLATOR ||
                            (appMode === AppMode.SEARCH && query.trim().length > 0);

    // 注意：窗口紧凑模式的设置已移动到各个具体页面组件中处理
    // AI聊天模式的紧凑模式设置现在在 ai-chat.tsx 组件中处理
  }, [query, appMode]);

  // 应用列表状态
  const [allApps, setAllApps] = useState<any[]>([]);
  
  // 获取应用列表
  const getAllApps = async () => {
    const allAppsList = await appManagerClient.getAppList()
    setAllApps(allAppsList);
  }

  // 删除拖入的文件
  const handleRemoveFiles = () => {
    setDroppedFiles([]); // 清除本地状态
    setGlobalDroppedFilePath(null); // 清除全局状态
    console.log('🗑️ 已清除本地和全局文件路径状态');
  };

  // 删除单个文件
  const handleRemoveSingleFile = (indexToRemove: number) => {
    const newFiles = droppedFiles.filter((_, index) => index !== indexToRemove);
    setDroppedFiles(newFiles);

    // 同步更新全局状态
    if (newFiles.length === 0) {
      setGlobalDroppedFilePath(null);
    } else {
      setGlobalDroppedFilePath(newFiles.join(','));
    }

    console.log(`🗑️ 已删除文件索引 ${indexToRemove}，剩余 ${newFiles.length} 个文件`);
  };

  // 获取文件名（从路径中提取）
  const getFileName = (filePath: string) => {
    return filePath.split(/[/\\]/).pop() || filePath;
  };

  // 截取文件名用于显示（保持合适的长度）
  const getTruncatedFileName = (filePath: string, maxLength: number = 20) => {
    const fileName = getFileName(filePath);
    if (fileName.length <= maxLength) {
      return fileName;
    }

    // 查找最后一个点的位置（文件扩展名）
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1) {
      // 没有扩展名，直接截取
      return fileName.substring(0, maxLength - 3) + '...';
    }

    const extension = fileName.substring(lastDotIndex);
    const nameWithoutExt = fileName.substring(0, lastDotIndex);

    // 计算可用于文件名主体的长度
    const availableLength = maxLength - extension.length - 3; // 3 for "..."

    if (availableLength <= 0) {
      // 扩展名太长，只显示扩展名
      return '...' + extension;
    }

    return nameWithoutExt.substring(0, availableLength) + '...' + extension;
  };

  // 生成文件显示文本
  const getFileDisplayText = () => {
    if (droppedFiles.length === 0) return null;
    if (droppedFiles.length === 1) {
      return getTruncatedFileName(droppedFiles[0], 12); // 限制单文件显示长度
    }
    return `${droppedFiles.length}个文件`;
  };

  // 生成Tooltip内容
  const getFileTooltipContent = () => {
    if (droppedFiles.length <= 1) return null;
    return (
      <div className="max-w-xs space-y-1">
        {droppedFiles.map((filePath, index) => (
          <div
            key={index}
            className="flex items-center justify-between group px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <span className="text-sm text-gray-700 dark:text-gray-300 truncate mr-2">
              {getFileName(filePath)}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-100 dark:hover:bg-red-900/30"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveSingleFile(index);
              }}
              title="删除此文件"
            >
              <X className="h-3 w-3 text-red-500 dark:text-red-400" />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  // 返回首页（内部实现）
  const internalHandleBackToHome = async () => {
      // 如果当前在AI对话模式，需要断开WebSocket连接
      if (appMode === AppMode.AI_CHAT) {
        // 确保WebSocket连接被完全断开
        try {
          const { default: WebSocketChatService } = await import('../../../llm/services/WebSocketChatService');
          WebSocketChatService.cleanup();
          console.log('🔌 返回首页时已完全断开WebSocket连接');
        } catch (error) {
          console.error('断开WebSocket连接失败:', error);
        }
      }

    focusAndReset();
    setMcpToolTitle(null); // 清除MCP技能标题
    setMcpToolInfo(null); // 清除MCP技能信息
    setDroppedFiles([]); // 清除拖入的文件
    setGlobalDroppedFilePath(null); // 清除全局文件路径状态
    setAppMode(AppMode.HOME);
  };

  // 在当前AI对话模式下继续对话（不触发路由切换）
  const startAiConversationInCurrentMode = useCallback((q?: string) => {
    console.log('🚀 startAiConversationInCurrentMode被调用，查询:', q);

    // 直接获取最新的认证状态
    const { isLoggedIn, user, token } = useAuthStore.getState();
    if (!isLoggedIn || !user || !token) {
      console.log('❌ 用户未登录，弹出登录框');
      useAuthStore.getState().openLoginDialog();
      setSearchBarDisabled(false);
      return;
    }

    // 在AI对话模式下，我们需要一个特殊的机制来触发新对话
    // 使用一个唯一的标识符来确保每次都是新的对话
    if (q?.trim()) {
      const uniqueQuery = `${q}#${Date.now()}`;
      console.log('🎯 设置唯一query以触发新对话:', uniqueQuery);
      setQuery(uniqueQuery);
    }
  }, [setQuery]);

  // 封装的方法用于发起AI对话（从其他模式切换到AI对话模式）
  const startAiConversation = useCallback((q?: string) => {
    console.log('🚀 startAiConversation被调用，查询:', q);

    // 直接获取最新的认证状态，而不是依赖requireAuth函数
    const { isLoggedIn, user, token } = useAuthStore.getState();
    // 检查登录状态
    if (!isLoggedIn || !user || !token) {
      console.log('❌ startAiConversation: 用户未登录，弹出登录框');
      useAuthStore.getState().openLoginDialog();
      setSearchBarDisabled(false);
      return;
    }
    console.log('✅ startAiConversation: 用户已登录，继续执行');

    setSearchBarDisabled(true);// 防止重复提交

    // 首先切换到AI对话模式，确保组件被渲染
    setAppMode(AppMode.AI_CHAT);

    // 如果有查询内容，等待组件渲染完成后再发起对话
    if (q?.trim()) {
      setTimeout(() => {
        // AI聊天现在通过路由系统处理
        if (externalOnRouteChange) {
          externalOnRouteChange('/ai-chat');
        }
      }, 100); // 等待100ms让组件渲染完成
    } else {
      // 没有查询内容时，只是切换到AI对话模式，等待用户输入或MCP请求
      console.log('切换到AI对话模式，等待用户输入或MCP请求');
    }
  }, [setAppMode]);

  // 全局键盘事件监听（仅在剪切板历史模式下）
  useEffect(() => {
    if (appMode !== AppMode.CLIPBOARD_HISTORY) {
      return;
    }

    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      const inputElement = inputRef.current?.input;
      const isInputFocused = e.target === inputElement;

      // 处理上下键导航（垂直布局 - 居中窗口）
      // 即使输入框聚焦也要响应上下键
      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();
        // 这里不需要处理具体的导航逻辑，让 useClipboardHistory hook 处理
        // 但我们需要阻止默认行为并让事件继续传播
        return;
      }

      // 如果是可输入的字符（字母、数字、符号等），自动聚焦到搜索框
      if (e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey && !isInputFocused) {
        e.preventDefault();
        if (inputRef.current) {
          inputRef.current.focus();
          // 将字符添加到搜索框
          const newQuery = query + e.key;
          if (externalOnQueryChange) {
            externalOnQueryChange(newQuery);
          } else {
            setInternalQuery(newQuery);
          }
        }
        return;
      }

      // 处理ESC键清空搜索
      if (e.key === 'Escape') {
        e.preventDefault();
        if (query) {
          setQuery('');
          if (inputRef.current) {
            inputRef.current.blur();
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);

    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [appMode, query]);

  // 同步appMode到ref
  useEffect(() => {
    appModeRef.current = appMode;
  }, [appMode]);
  const { onMouseDown } = useDrag();
  return (
    <div className="w-full max-w-full">
      <div onMouseDown={onMouseDown} style={{zIndex: 1000, position: 'absolute', top: 0, left: 0, width: '100%', height: '10px',cursor: 'move'}}></div>
      <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 rounded-t-lg p-2">
            {/* 当处于AI模式、文件搜索模式、剪贴板历史模式或翻译模式时显示回退按钮 */}
            {(appMode === AppMode.AI_CHAT || appMode === AppMode.FILE_SEARCH || appMode === AppMode.CLIPBOARD_HISTORY || appMode === AppMode.TRANSLATOR) && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 mr-1"
                onClick={handleBackToHome}
                title={t('common.back')}
              >
                <ArrowLeft className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              </Button>
            )}

            {/* 当处于AI对话模式且有MCP技能标题时显示技能名称 */}
            {appMode === AppMode.AI_CHAT && mcpToolTitle && (
              <div className="flex items-center mr-2">
                <div className="flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/40 dark:to-indigo-900/40 px-3 py-1.5 rounded-lg border border-blue-200 dark:border-blue-700/50 shadow-sm">
                  {/* 根据技能信息显示对应图标，如果有logoUrl则显示图片，否则显示默认图标 */}
                  {mcpToolInfo?.logoUrl ? (
                    <img
                      src={mcpToolInfo.logoUrl}
                      alt={mcpToolTitle}
                      className="h-4 w-4 mr-2 rounded-sm"
                      onError={(e) => {
                        // 如果图片加载失败，显示默认图标
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null}
                  <Wrench className={`h-4 w-4 text-blue-600 dark:text-blue-400 mr-2 ${mcpToolInfo?.logoUrl ? 'hidden' : ''}`} />
                  <span className="text-sm font-semibold text-blue-800 dark:text-blue-200">
                    {mcpToolTitle}
                  </span>
                </div>
              </div>
            )}

            {/* 当有拖入文件时显示文件信息 */}
            {droppedFiles.length > 0 && (
              <div className="flex items-center mr-2">
                {droppedFiles.length === 1 ? (
                  // 单个文件直接显示
                  <div className="flex items-center bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/40 dark:to-emerald-900/40 px-3 py-1.5 rounded-lg border border-green-200 dark:border-green-700/50 shadow-sm">
                    <File className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                    <span title={droppedFiles[0]} className="text-sm font-semibold text-green-800 dark:text-green-200">
                      {getFileDisplayText()}
                    </span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-2 h-6 w-6 p-0 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors"
                      onClick={handleRemoveFiles}
                      title="删除文件"
                    >
                      <X className="h-3 w-3 text-green-600 dark:text-green-400" />
                    </Button>
                  </div>
                ) : (
                  // 多个文件使用Tooltip显示列表
                  <ConfigProvider
                    theme={{
                      components: {
                        Tooltip: {
                          // 明亮模式：白色背景；暗色模式：深色背景（跟随主题风格）
                          colorBgSpotlight: isDarkMode ? 'rgba(0, 0, 0, 0.85)' : 'rgba(255, 255, 255, 0.95)',
                          colorTextLightSolid: isDarkMode ? '#fff' : '#000',
                        }
                      }
                    }}
                  >
                    <Tooltip
                      title={getFileTooltipContent()}
                      placement="bottom"
                    >
                      <div className="flex items-center bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/40 dark:to-emerald-900/40 px-3 py-1.5 rounded-lg border border-green-200 dark:border-green-700/50 shadow-sm cursor-pointer">
                        <Files className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                        <span className="text-sm font-semibold text-green-800 dark:text-green-200">
                          {getFileDisplayText()}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="ml-2 h-6 w-6 p-0 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveFiles();
                          }}
                          title="删除所有文件"
                        >
                          <X className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </Button>
                      </div>
                    </Tooltip>
                  </ConfigProvider>
                )}
              </div>
            )}

            <Input
              ref={inputRef}
              value={query}
              type="text"
              placeholder={
                searchBarDisabled
                  ? t('ai.processing')
                  : appMode === AppMode.FILE_SEARCH
                    ? t('fileSearch.placeholder')
                    : appMode === AppMode.CLIPBOARD_HISTORY
                      ? t('clipboard.searchPlaceholder')
                      : t('search.placeholder')
              }
              className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 !transition-none"
              onChange={handleQueryChange}
              onKeyDown={handleKeyDown}
              onCompositionStart={handleCompositionStart}
              onCompositionUpdate={handleCompositionUpdate}
              onCompositionEnd={handleCompositionEnd}
              disabled={searchBarDisabled}
              autoFocus={!searchBarDisabled && appMode !== AppMode.CLIPBOARD_HISTORY}
              id="search-bar-input"
            />
            <div className="flex space-x-1">
              {/* <Button
                variant={activeTools.tools ? "default" : "ghost"}
                size="icon"
                className={`h-8 w-8 rounded-md border ${activeTools.tools ? 'bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800 dark:hover:bg-blue-800' : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-150 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'}`}
                onClick={() => toggleTool('tools')}
                title={t('tools.useTools')}
                disabled={searchBarDisabled}
              >
                <Wrench className="h-4 w-4" />
              </Button>
              <Button
                variant={activeTools.network ? "default" : "ghost"}
                size="icon"
                className={`h-8 w-8 rounded-md border ${activeTools.network ? 'bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800 dark:hover:bg-blue-800' : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-150 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'}`}
                onClick={() => toggleTool('network')}
                title={t('tools.network')}
                disabled={searchBarDisabled}
              >
                <Globe className="h-4 w-4" />
              </Button> */}
              <Button
                variant={activeTools.pin ? "default" : "ghost"}
                size="icon"
                className={`h-8 w-8 rounded-md border ${activeTools.pin ? 'bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800 dark:hover:bg-blue-800' : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-150 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'}`}
                onClick={() => toggleTool('pin')}
                title={t('tools.pin')}
                disabled={searchBarDisabled}
              >
                <Pin className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
  );
};

// 添加displayName以便调试
SearchBar.displayName = 'SearchBar';

export default SearchBar; 